<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>cn.com.chinastock</groupId>
        <artifactId>galaxy-boot-parent-jdk8</artifactId>
        <version>${revision}</version>
        <relativePath>galaxy-boot-parent/pom.xml</relativePath>
    </parent>

    <artifactId>galaxy-boot-jdk8</artifactId>
    <packaging>pom</packaging>
    <name>Galaxy Boot</name>
    <description>Galaxy Boot</description>
    <url>https://gitlab.chinastock.com.cn/it-infrastructure/galaxy-boot</url>

    <scm>
        <url>https://gitlab.chinastock.com.cn/it-infrastructure/galaxy-boot</url>
        <connection>
            scm:git:git://gitlab.chinastock.com.cn/it-infrastructure/galaxy-boot.git
        </connection>
        <developerConnection>
            scm:git:ssh://****************************/it-infrastructure/galaxy-boot.git
        </developerConnection>
        <tag>HEAD</tag>
    </scm>

    <properties>
        <!--  项目统一版本号 -->
        <testng.version>7.10.2</testng.version>

        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <!-- Maven Plugin Versions -->
        <maven-checkstyle-plugin.version>3.6.0</maven-checkstyle-plugin.version>
        <!-- JUnit 5 requires Surefire version 2.22.0 or higher -->
        <maven-surefire-plugin.version>3.5.2</maven-surefire-plugin.version>

        <!-- 文档化 -->
        <git-changelog-maven-plugin.version>1.101.0</git-changelog-maven-plugin.version>
        <dokka-maven-plugin.version>1.9.20</dokka-maven-plugin.version>

        <!-- 处理 ${revision} 未能正确处理 -->
        <flatten-maven-plugin.version>1.6.0</flatten-maven-plugin.version>

        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

        <spring-javaformat.version>0.0.31</spring-javaformat.version>
        <githook-maven-plugin.version>1.0.5</githook-maven-plugin.version>
        <git-build-hook-maven-plugin.version>3.5.0</git-build-hook-maven-plugin.version>
        <exec-maven-plugin.version>3.5.0</exec-maven-plugin.version>
        <build-helper-maven-plugin.version>3.6.0</build-helper-maven-plugin.version>
    </properties>

    <modules>
        <module>galaxy-boot-parent</module>
        <module>galaxy-boot-core</module>
        <module>galaxy-boot-security</module>
        <module>galaxy-boot-utils</module>
<!--        <module>galaxy-boot-test</module>-->

        <module>galaxy-boot-dependencies</module>

        <module>galaxy-boot-starters</module>

        <module>galaxy-boot-examples</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>cn.com.chinastock</groupId>
                <artifactId>galaxy-boot-dependencies-jdk8</artifactId>
                <version>${project.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <version>${junit-jupiter.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.assertj</groupId>
            <artifactId>assertj-core</artifactId>
            <version>${assertj-core.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.ctrip.framework.apollo</groupId>
            <artifactId>apollo-client</artifactId>
            <version>${apollo-client.version}</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.jacoco</groupId>
                    <artifactId>jacoco-maven-plugin</artifactId>
                    <version>${jacoco.version}</version>
                </plugin>

                <!-- disable auto format from parent -->
                <plugin>
                    <groupId>io.spring.javaformat</groupId>
                    <artifactId>spring-javaformat-maven-plugin</artifactId>
                    <version>${spring-javaformat.version}</version>
                    <executions>
                        <execution>
                            <phase>validate</phase>
                            <configuration>
                                <skip>true</skip>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-checkstyle-plugin</artifactId>
                <version>${maven-checkstyle-plugin.version}</version>
                <executions>
                    <execution>
                        <id>checkstyle-validation</id>
                        <phase>validate</phase>
                        <goals>
                            <goal>check</goal>
                        </goals>
                        <configuration>
                            <configLocation>checkstyle.xml</configLocation>
                            <includeTestSourceDirectory>true</includeTestSourceDirectory>
                            <consoleOutput>true</consoleOutput>
                            <failsOnError>true</failsOnError>
                            <failOnViolation>true</failOnViolation>
                            <violationSeverity>warning</violationSeverity>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven-compiler-plugin.version}</version>
                <inherited>true</inherited>
                <configuration>
                    <source>${maven.compiler.source}</source>
                    <target>${maven.compiler.target}</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>${maven-surefire-plugin.version}</version>
                <inherited>true</inherited>
                <configuration>
                    <forkCount>1</forkCount>
                    <reuseForks>false</reuseForks>
                    <includes>
                        <include>**/*Test*.java</include>
                    </includes>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>${flatten-maven-plugin.version}</version>
                <configuration>
                    <updatePomFile>true</updatePomFile>
                    <flattenMode>resolveCiFriendliesOnly</flattenMode>
                </configuration>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <!-- GitHook -->
            <plugin>
                <groupId>io.github.phillipuniverse</groupId>
                <artifactId>githook-maven-plugin</artifactId>
                <version>${githook-maven-plugin.version}</version>
                <inherited>false</inherited>
                <executions>
                    <execution>
                        <goals>
                            <goal>install</goal>
                        </goals>
                        <configuration>
                            <hooks>
                                <pre-push>
                                    exec mvn clean test package
                                </pre-push>
                            </hooks>
                        </configuration>
                    </execution>
                </executions>
                <configuration>
                    <!--是否跳过-->
                    <skip>false</skip>
                </configuration>
            </plugin>
            <!-- git-build -->
            <plugin>
                <groupId>com.rudikershaw.gitbuildhook</groupId>
                <artifactId>git-build-hook-maven-plugin</artifactId>
                <version>${git-build-hook-maven-plugin.version}</version>
                <inherited>false</inherited>
                <configuration>
                    <installHooks>
                        <prepare-commit-msg>.mvn/.githooks/prepare-commit-msg</prepare-commit-msg>
                    </installHooks>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>install</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>exec-maven-plugin</artifactId>
                <version>${exec-maven-plugin.version}</version>
                <inherited>false</inherited>
                <executions>
                    <execution>
                        <id>script-chmod</id>
                        <phase>package</phase>
                        <goals>
                            <goal>exec</goal>
                        </goals>
                        <configuration>
                            <executable>chmod</executable>
                            <arguments>
                                <argument>755</argument>
                                <argument>${basedir}/.mvn/.githooks/prepare-commit-msg</argument>
                            </arguments>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
                <version>${build-helper-maven-plugin.version}</version>
                <executions>
                    <execution>
                        <id>add-test-source</id>
                        <phase>generate-test-sources</phase>
                        <goals>
                            <goal>add-test-source</goal>
                        </goals>
                        <configuration>
                            <sources>
                                <source>src/integrationTest/java</source>
                            </sources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <version>${maven-deploy-plugin.version}</version>
            </plugin>
        </plugins>
    </build>

    <reporting>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-checkstyle-plugin</artifactId>
            </plugin>
        </plugins>
    </reporting>

    <profiles>
        <profile>
            <id>release</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-source-plugin</artifactId>
                        <version>${maven-source-plugin.version}</version>
                        <executions>
                            <execution>
                                <phase>package</phase>
                                <goals>
                                    <goal>jar-no-fork</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-javadoc-plugin</artifactId>
                        <version>${maven-javadoc-plugin.version}</version>
                        <executions>
                            <execution>
                                <phase>package</phase>
                                <goals>
                                    <goal>jar</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>

                </plugins>
            </build>
        </profile>
        <profile>
            <id>local</id>
            <distributionManagement>
                <repository>
                    <id>local.repo</id>
                    <name>Local Repository</name>
                    <url>file:${project.build.directory}/local-repo</url>
                </repository>
            </distributionManagement>
        </profile>
    </profiles>

    <distributionManagement>
        <repository>
            <id>release</id>
            <name>maven-release</name>
            <url>https://bkrepo.chinastock.com.cn/maven/n614c6/maven-release/</url>
        </repository>
    </distributionManagement>
</project>
