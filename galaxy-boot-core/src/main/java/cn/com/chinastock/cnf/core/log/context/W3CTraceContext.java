package cn.com.chinastock.cnf.core.log.context;

import cn.com.chinastock.cnf.core.log.utils.TraceUtils;
import org.springframework.util.StringUtils;

import java.util.*;

import static cn.com.chinastock.cnf.core.log.context.TraceConstants.TRACE_PARENT;

/**
 * W3C Trace Context实现
 *
 * <AUTHOR>
 */
public class W3CTraceContext implements ITraceContext {

    private String traceId;
    private String spanId;
    private String parentSpanId;

    public W3CTraceContext() {
        traceId = null;
        spanId = null;
        parentSpanId = null;
    }

    @Override
    public String getTraceId() {
        return traceId;
    }

    @Override
    public String getSpanId() {
        return spanId;
    }

    @Override
    public String getParentSpanId() {
        return parentSpanId;
    }

    @Override
    public void extractTraceContext(Map<String, String> headers) {
        if (null == headers || headers.isEmpty()) {
            initTrace();
            return;
        }

        String traceparent = headers.get(TRACE_PARENT);
        if (traceparent == null || traceparent.isEmpty()) {
            initTrace();
            return;
        }

        String[] parts = traceparent.split("-");
        if (parts.length != 4) {
            initTrace();
            return;
        }

        traceId = parts[1];
        parentSpanId = parts[2];
        spanId = generateSpanId();
    }

    @Override
    public Map<String, String> generateTraceHeaders(String traceId, String spanId) {
        if (!StringUtils.hasText(traceId) && !StringUtils.hasText(spanId)) {
            return new HashMap<>();
        }
        HashMap<String, String> headers = new HashMap<>();
        headers.put(TRACE_PARENT, TraceUtils.generateTraceParent(traceId, spanId));
        return headers;
    }

    /**
     * 初始化新的trace context
     */
    private void initTrace() {
        generateTraceId();
        generateSpanId();
        parentSpanId = null;
    }

    @Override
    public void clear() {
        traceId = null;
        spanId = null;
        parentSpanId = null;
    }

    @Override
    public List<String> getTraceHeaders() {
        return new ArrayList<String>() {{
            add(TRACE_PARENT);
        }};
    }

    @Override
    public String generateTraceId() {
        traceId = TraceUtils.generateTraceId();
        return traceId;
    }

    @Override
    public String generateSpanId() {
        spanId = TraceUtils.generateSpanId();
        return spanId;
    }

    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }

    public void setSpanId(String spanId) {
        this.spanId = spanId;
    }

    public void setParentSpanId(String parentSpanId) {
        this.parentSpanId = parentSpanId;
    }
}