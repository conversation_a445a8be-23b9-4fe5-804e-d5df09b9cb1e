package cn.com.chinastock.cnf.core.log.utils;

import java.nio.charset.StandardCharsets;

/**
 * 文件处理工具类
 */
public class HttpFileUtils {
    
    /**
     * 格式化文件大小为可读的字符串形式
     *
     * @param size 文件大小（字节）
     * @return 格式化后的文件大小字符串，如：1.5MB、2.3GB等
     */
    public static String formatFileSize(long size) {
        if (size < 1024) {
            return size + "B";
        } else if (size < 1024 * 1024) {
            return String.format("%.1fKB", size / 1024.0);
        } else if (size < 1024 * 1024 * 1024) {
            return String.format("%.1fMB", size / (1024.0 * 1024));
        } else {
            return String.format("%.1fGB", size / (1024.0 * 1024 * 1024));
        }
    }

    /**
     * 从Content-Disposition头中提取文件名
     *
     * @param contentDisposition Content-Disposition头的值
     * @return 提取的文件名，如果无法提取则返回null
     */
    public static String extractFilename(String contentDisposition) {
        if (contentDisposition == null) {
            return null;
        }

        String filename = extractUTF8Filename(contentDisposition);
        if (filename == null) {
            filename = extractRegularFilename(contentDisposition);
        }

        return filename != null ? decodeFilename(filename) : null;
    }

    /**
     * 从Content-Disposition中提取UTF-8编码的文件名
     *
     * @param contentDisposition Content-Disposition头的值
     * @return UTF-8编码的文件名，如果不存在则返回null
     */
    private static String extractUTF8Filename(String contentDisposition) {
        if (!contentDisposition.contains("filename*=UTF-8''")) {
            return null;
        }

        int startIndex = contentDisposition.indexOf("filename*=UTF-8''") + "filename*=UTF-8''".length();
        return extractFilenameValue(contentDisposition, startIndex);
    }

    /**
     * 从Content-Disposition中提取普通文件名
     *
     * @param contentDisposition Content-Disposition头的值
     * @return 普通文件名，如果不存在则返回null
     */
    private static String extractRegularFilename(String contentDisposition) {
        if (!contentDisposition.contains("filename=")) {
            return null;
        }

        int startIndex = contentDisposition.indexOf("filename=") + "filename=".length();
        String filename = extractFilenameValue(contentDisposition, startIndex);
        return filename != null ? filename.replace("\"", "") : null;
    }

    /**
     * 从指定位置提取文件名值
     *
     * @param contentDisposition Content-Disposition头的值
     * @param startIndex 开始提取的位置
     * @return 提取的文件名值，如果无法提取则返回null
     */
    private static String extractFilenameValue(String contentDisposition, int startIndex) {
        int endIndex = contentDisposition.indexOf(";", startIndex);
        if (endIndex == -1) {
            endIndex = contentDisposition.length();
        }
        return startIndex < endIndex ? contentDisposition.substring(startIndex, endIndex) : null;
    }

    /**
     * URL解码文件名
     *
     * @param filename 需要解码的文件名
     * @return 解码后的文件名，如果解码失败则返回原始文件名
     */
    private static String decodeFilename(String filename) {
        try {
            return java.net.URLDecoder.decode(filename, StandardCharsets.UTF_8.name());
        } catch (Exception e) {
            return filename;
        }
    }
} 