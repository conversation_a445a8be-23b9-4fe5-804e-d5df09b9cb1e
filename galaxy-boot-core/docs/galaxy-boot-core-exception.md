## Galaxy Boot Exception

### 设计说明

根据 [Http返回码规范](http://pmc.chinastock.com.cn/confluence/pages/viewpage.action?pageId=197272010) 定义，`Galaxy Boot`在框架中会对异常进行统一处理，返回对应的 Http 状态码。
- 正常情况下，返回 200。
- 业务异常时，返回 200，具体业务错误码通过响应体中`Meta`信息进行判断。
- 客户端异常时，三种情况：
  - 参数校验异常，返回 400。
  - 认证校验失败，返回 401。
  - 接口鉴权失败，返回 403。
- 服务端异常时，返回 500。

### 异常定义

`Galaxy Boot`框架中定义了以下几种异常：

#### 业务异常
- `BusinessException`：业务侧异常，返回200状态码
  - 当未指定Meta.code时，使用系统默认code：`{systemCode}TCNF200`
  - 当未指定Meta.message时，使用默认消息："业务异常"

#### 认证鉴权异常
- `UnauthorizedException`：认证异常，返回401状态码
  - 当未指定Meta.code时，使用系统默认code：`{systemCode}TCNF401`
  - 当未指定Meta.message时，使用默认消息："认证失败"
- `ForbiddenException`：鉴权异常，返回403状态码
  - 当未指定Meta.code时，使用系统默认code：`{systemCode}TCNF403`
  - 当未指定Meta.message时，使用默认消息："无权限访问"

#### 服务端异常
- `ServerErrorException`：服务端异常，返回500状态码
  - 当未指定Meta.code时，使用系统默认code：`{systemCode}TCNF500`
  - 当未指定Meta.message时，使用默认消息："应用服务端异常"
- `GalaxyFeignException`：Feign调用异常，返回500状态码
  - 使用系统默认Meta.code：`{systemCode}TCNF500`
  - 当feign异常Meta.code为空时，Meta.message为"调用三方服务异常"
  - 当feign异常Meta.code不为空时，Meta.message为"调用三方服务异常[code]"

#### 其他异常处理
- `RuntimeException`：运行时异常，返回500状态码
  - 使用系统默认Meta.code：`{systemCode}TCNF500`
  - 使用默认Meta.message："未知应用服务端异常"
- 常见异常（如IOException、SQLException等）：返回500状态码
  - 使用系统默认Meta.code：`{systemCode}TCNF500`
  - 使用默认Meta.message："未知应用服务端异常"
- `MethodArgumentNotValidException`：参数校验异常，返回400状态码
  - 使用系统默认Meta.code：`{systemCode}TCNF400`
  - Meta.message为所有验证错误信息的组合

其中`{systemCode}`为系统的三字码，可以通过`application.yml`中`galaxy.system.code`进行配置。

### 使用说明

#### 异常抛出
业务开发过程中，可以根据业务逻辑选择抛出对应的异常：

```java
// 抛出业务异常，指定code和message，可以选择是否传递详细异常信息，传递会打印在日志中
throw new BusinessException("XXXB1001", "业务异常");
throw new BusinessException("XXXB1001", "业务异常", e); // e为详细异常信息

// 抛出认证异常，可以选择是否传递Meta.code，以及详细异常信息，不传递时走默认逻辑
throw new UnauthorizedException("用户未登录");
throw new UnauthorizedException("AUTH001", "用户未登录");
throw new UnauthorizedException("AUTH001", "用户未登录", e); // e为详细异常信息

// 抛出鉴权异常，可以选择是否传递Meta.code，以及详细异常信息，不传递时走默认逻辑
throw new ForbiddenException("无权访问该资源");
throw new ForbiddenException("FORB001", "无权访问该资源");
throw new ForbiddenException("FORB001", "无权访问该资源", e); // e为详细异常信息

// 抛出服务端异常，可以选择是否传递Meta.code，以及详细异常信息，不传递时走默认逻辑
throw new ServerErrorException("调用三方服务超时");
throw new ServerErrorException("SERV001", "调用三方服务超时");
throw new ServerErrorException("SERV001", "调用三方服务超时", e); // e为详细异常信息
```

#### GalaxyFeignException说明
当Feign调用异常时
- 如果在响应 Body 中可以按照 BaseResponse 解析出 Meta 信息，则会抛出 `GalaxyFeignException` 异常。
- 如果无法解析出 Meta 信息，则会按照正常处理逻辑，抛出 `FeignException` 异常。

`GalaxyFeignException` 继承自 `FeignException`, 因此在捕获到 `GalaxyFeignException` 时，可以获取到 `FeignException` 相关的信息。

异常处理示例：
```java
try {
    // Feign调用
    BaseResponse response = feignClient.call();
} catch (GalaxyFeignException e) {
    // 获取异常信息
    String code = e.getMeta().getCode();
    String message = e.getMeta().getMessage();
    // 进行接下来的逻辑处理
    ...
} catch (FeignException e) {
    // 针对FeignException的处理
    ...
}
```

#### 参数校验
使用 Jakarta Validation 注解进行参数校验：

```java
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;

public class UserDTO {
    @NotBlank(message = "名称不能为空")
    private String name;

    @Email(message = "邮箱格式不正确")
    private String email;

    @Min(value = 18, message = "年龄必须大于或等于18")
    private Integer age;
}

@PostMapping("/user")
public UserDTO createUser(@Validated @RequestBody UserDTO user) {
    return user;
}
```

当校验失败时，会返回400状态码，并在message中包含所有的验证错误信息。

#### 异常日志
框架会自动记录所有异常的日志：
- 业务异常：记录code和message，当异常不为空时，记录详细异常信息
- 认证鉴权异常：记录code和message，当异常不为空时，记录详细异常信息
- Feign调用异常：记录code、message和详细异常信息
- 其他异常：记录异常message和堆栈信息

所有异常日志使用 `LogCategory.EXCEPTION_LOG` 类别进行记录。


