<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>cn.com.chinastock</groupId>
        <artifactId>galaxy-boot-examples</artifactId>
        <version>${revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>galaxy-boot-core-example</artifactId>
    <packaging>jar</packaging>
    <name>Galaxy Boot Core Example</name>

    <dependencies>
        <dependency>
            <groupId>cn.com.chinastock</groupId>
            <artifactId>galaxy-boot-core-jdk8</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.com.chinastock</groupId>
            <artifactId>galaxy-boot-security-jdk8</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.com.chinastock</groupId>
            <artifactId>galaxy-boot-utils-jdk8</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.com.chinastock</groupId>
            <artifactId>galaxy-boot-starter-tongweb-jdk8</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.com.chinastock</groupId>
            <artifactId>galaxy-boot-starter-feign-jdk8</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.com.chinastock</groupId>
            <artifactId>galaxy-boot-starter-data-oceanbase-jdk8</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.com.chinastock</groupId>
            <artifactId>galaxy-boot-starter-fastjson-jdk8</artifactId>
        </dependency>


        <dependency>
            <groupId>cn.com.chinastock</groupId>
            <artifactId>galaxy-boot-starter-metrics-jdk8</artifactId>
        </dependency>

        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>context-propagation</artifactId>
            <version>1.1.3</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ctrip.framework.apollo</groupId>
            <artifactId>apollo-client</artifactId>
            <version>${apollo-client.version}</version>
        </dependency>

        <dependency>
            <groupId>cn.com.chinastock</groupId>
            <artifactId>galaxy-boot-starter-swagger-jdk8</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <version>${maven-deploy-plugin.version}</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>