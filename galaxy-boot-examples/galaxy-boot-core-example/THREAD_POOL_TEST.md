# ThreadPoolController 测试说明

## 概述

ThreadPoolController 是一个用于测试多线程处理时 TraceId 传递的控制器。它实现了以下功能：

1. **批量消息处理**：通过线程池并发处理多个消息
2. **ContextPropagatingExecutor**：确保线程池中的任务能够正确传递 traceId、spanId 等信息
3. **结果汇总**：收集所有任务的处理结果并返回统计信息

## API 接口

### 批量消息处理

**接口地址：** `POST /api/test/thread-pool/batch-message`

**请求头：**
```
Content-Type: application/json
X-Trace-Id: {traceId}  // 可选，用于测试 traceId 传递
```

**请求体：**
```json
{
  "messages": [
    "消息1",
    "消息2",
    "消息3",
    "消息4",
    "消息5"
  ]
}
```

**响应体：**
```json
{
  "data": {
    "totalCount": 5,
    "successCount": 5,
    "failureCount": 0,
    "results": [
      {
        "taskId": 1,
        "message": "已处理: 消息1 (任务ID: 1)",
        "success": true,
        "errorMessage": null
      },
      {
        "taskId": 2,
        "message": "已处理: 消息2 (任务ID: 2)",
        "success": true,
        "errorMessage": null
      }
    ]
  },
  "meta": {
    "success": true,
    "code": "0",
    "message": "批量消息处理完成"
  }
}
```

## 测试方法

### 1. 基本功能测试

```bash
curl -X POST http://localhost:8088/api/test/thread-pool/batch-message \
  -H "Content-Type: application/json" \
  -d '{"messages": ["消息1", "消息2", "消息3", "消息4", "消息5"]}'
```

### 2. TraceId 传递测试

```bash
curl -X POST http://localhost:8088/api/test/thread-pool/batch-message \
  -H "Content-Type: application/json" \
  -H "X-Trace-Id: test-trace-id-12345" \
  -d '{"messages": ["带TraceId的消息1", "带TraceId的消息2", "带TraceId的消息3"]}'
```

### 3. 大量消息测试

```bash
curl -X POST http://localhost:8088/api/test/thread-pool/batch-message \
  -H "Content-Type: application/json" \
  -d '{"messages": ["消息1", "消息2", "消息3", "消息4", "消息5", "消息6", "消息7", "消息8", "消息9", "消息10"]}'
```

## 核心实现

### ContextPropagatingExecutor

ContextPropagatingExecutor 是一个包装了标准 ExecutorService 的执行器，它确保：

1. **上下文捕获**：在提交任务时捕获当前线程的 traceId、spanId、parentSpanId
2. **上下文传递**：在新线程中设置这些上下文信息
3. **上下文清理**：任务完成后清理 MDC 中的上下文信息

### 线程池配置

- **核心线程数**：5
- **最大线程数**：10
- **空闲线程存活时间**：60秒
- **工作队列**：LinkedBlockingQueue(100)
- **拒绝策略**：CallerRunsPolicy

### 日志输出

在处理过程中，会输出以下日志：

1. **主线程日志**：开始批量处理消息
2. **线程池任务日志**：每个任务开始和完成时的日志
3. **汇总日志**：处理完成后的统计信息

所有日志都会包含正确的 traceId、spanId 等信息，便于链路追踪。

## 验证 TraceId 传递

通过查看应用程序日志，可以验证：

1. **主线程日志**：包含请求的 traceId
2. **线程池任务日志**：包含相同的 traceId
3. **日志格式**：符合 Galaxy Boot 的日志格式规范

## 注意事项

1. 应用程序运行在端口 8088 上
2. 每个任务会模拟 100-500ms 的处理时间
3. 任务超时时间设置为 30 秒
4. 如果任务执行失败，会在结果中记录错误信息 