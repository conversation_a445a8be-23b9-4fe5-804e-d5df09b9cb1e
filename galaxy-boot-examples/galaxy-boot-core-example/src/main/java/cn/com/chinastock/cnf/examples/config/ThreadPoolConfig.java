package cn.com.chinastock.cnf.examples.config;

import cn.com.chinastock.cnf.core.log.GalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import io.micrometer.context.ContextExecutorService;
import javax.annotation.PreDestroy;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.*;

/**
 * 线程池配置类 - 简单示例，展示如何配置支持链路追踪的线程池
 *
 * <AUTHOR> Boot Team
 */
@Configuration
public class ThreadPoolConfig {

    /**
     * 消息处理线程池 - 用于演示多线程环境下的链路追踪
     *
     * @return 支持链路追踪的线程池
     */
    @Bean(name = "messageProcessorExecutor")
    public ExecutorService messageProcessorExecutor() {
        // 创建基础线程池
        ThreadPoolExecutor baseExecutor = new ThreadPoolExecutor(
            5,  // 核心线程数
            10, // 最大线程数
            60L, TimeUnit.SECONDS, // 空闲线程存活时间
            new LinkedBlockingQueue<>(100), // 任务队列
            new ThreadFactory() {
                private int counter = 0;
                @Override
                public Thread newThread(Runnable r) {
                    return new Thread(r, "message-processor-" + (++counter));
                }
            },
            new ThreadPoolExecutor.CallerRunsPolicy() // 拒绝策略：调用者运行
        );

        GalaxyLogger.info(LogCategory.FRAMEWORK_LOG, "创建消息处理线程池");

        // 关键：使用ContextExecutorService包装，支持上下文传播
        return ContextExecutorService.wrap(baseExecutor);
    }

    /**
     * 线程池管理器，用于优雅关闭线程池
     */
    @Configuration
    public static class ThreadPoolManager {

        private final ExecutorService messageProcessorExecutor;

        public ThreadPoolManager(ExecutorService messageProcessorExecutor) {
            this.messageProcessorExecutor = messageProcessorExecutor;
        }

        /**
         * 应用关闭时优雅关闭线程池
         */
        @PreDestroy
        public void shutdown() {
            GalaxyLogger.info(LogCategory.FRAMEWORK_LOG, "开始关闭线程池...");

            if (messageProcessorExecutor != null && !messageProcessorExecutor.isShutdown()) {
                GalaxyLogger.info(LogCategory.FRAMEWORK_LOG, "关闭消息处理线程池");
                messageProcessorExecutor.shutdown();
                try {
                    if (!messageProcessorExecutor.awaitTermination(10, TimeUnit.SECONDS)) {
                        messageProcessorExecutor.shutdownNow();
                    }
                } catch (InterruptedException e) {
                    messageProcessorExecutor.shutdownNow();
                    Thread.currentThread().interrupt();
                }
            }

            GalaxyLogger.info(LogCategory.FRAMEWORK_LOG, "线程池已关闭");
        }
    }
}
