package cn.com.chinastock.cnf.examples.controller;

import cn.com.chinastock.cnf.feign.exception.GalaxyFeignException;
import cn.com.chinastock.cnf.core.http.BaseResponse;
import cn.com.chinastock.cnf.core.http.Meta;
import cn.com.chinastock.cnf.core.log.GalaxyLogger;
import cn.com.chinastock.cnf.examples.client.ESBClient;
import cn.com.chinastock.cnf.examples.client.ExceptionClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/test/feign")
public class FeignController {

    private final ExceptionClient exceptionClient;

    private final ESBClient eSBClient;

    public FeignController(ExceptionClient exceptionClient, ESBClient eSBClient) {
        this.exceptionClient = exceptionClient;
        this.eSBClient = eSBClient;
    }

    @GetMapping("/timeout")
    public BaseResponse<Object> timeout() {
        eSBClient.timeout();
        return new BaseResponse<>(new Meta(true, "0", "success"), null);
    }

    @GetMapping("/esb")
    public BaseResponse<Object> callESB() {
        eSBClient.headers();
        return new BaseResponse<>(new Meta(true, "0", "success"), null);
    }

    @GetMapping("/feign-exception")
    public BaseResponse<Object> callNotFoundAPI() {
        try {
            exceptionClient.notFound();
        } catch (GalaxyFeignException e) {
            GalaxyLogger.error("GalaxyFeignException, code={}, message={}", e.getCode(), e.getMessage());
            return new BaseResponse<>(e.getMeta(), null);
        }
        return new BaseResponse<>(new Meta(true, "0", "success"), null);
    }

    @GetMapping("/galaxy-exception")
    public BaseResponse<Object> callException() {
        try {
            exceptionClient.forbidden();
        } catch (GalaxyFeignException e) {
            GalaxyLogger.error("GalaxyFeignException, code={}, message={}", e.getCode(), e.getMessage());
            return new BaseResponse<>(e.getMeta(), null);
        }
        return new BaseResponse<>(new Meta(true, "0", "success"), null);
    }
}
