package cn.com.chinastock.cnf.examples.controller;

import cn.com.chinastock.cnf.core.http.BaseResponse;
import cn.com.chinastock.cnf.core.http.Meta;
import cn.com.chinastock.cnf.core.log.GalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import cn.com.chinastock.cnf.examples.dto.LogDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.logging.LogLevel;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.core.io.UrlResource;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.MalformedURLException;
import java.util.Collections;
import java.util.List;

import static org.apache.logging.log4j.util.Strings.repeat;


@RestController
@RequestMapping("/api/test/log")
public class LogController {

    @Autowired
    private ResourceLoader resourceLoader;

    @GetMapping(value = "/gzip", params = {"length"})
    public LogDTO gzipResponse(@RequestParam(value = "length") int length) {
        String logData = String.join("", Collections.nCopies(Math.max(0, 1000), "a"));
        GalaxyLogger.info(LogCategory.APP_LOG, "gzip日志测试, length={}, data={}", length, logData);
        return new LogDTO(logData);
    }

    @GetMapping(value = "/exception")
    public LogDTO exception() {
        GalaxyLogger.error(LogCategory.EXCEPTION_LOG, "异常日志测试", new RuntimeException("异常日志测试"));
        return new LogDTO("异常日志测试");
    }

    @GetMapping(value = "/batch", params = {"length", "count"})
    public LogDTO batchLog(@RequestParam(value = "length") int length,
                           @RequestParam(value = "count") int count) {
        String logData = repeat("a", Math.max(0, length));
        count = count > 0 ? count : 10000;
        for (int i = 0; i < count; i++) {
            GalaxyLogger.info(LogCategory.APP_LOG, "日志测试, i={}, length={}, data={}", i, length, logData);
        }
        return new LogDTO(logData);
    }

    @GetMapping("/multi-line")
    public LogDTO multiLine() {
        String data = "第一行日志\n第二行日志\n第三行日志";
        GalaxyLogger.info(LogCategory.BUSINESS_LOG, data);
        return new LogDTO(data);
    }

    @PostMapping(value = "/upload")
    public LogDTO fileUpload(@RequestParam("files") List<MultipartFile> files) {
        for (MultipartFile file : files) {
            GalaxyLogger.info(LogCategory.BUSINESS_LOG, "文件上传: {}", file.getOriginalFilename());
        }
        return new LogDTO("文件上传成功");
    }

    @GetMapping(value = "/download")
    public ResponseEntity<UrlResource> fileDownload() {
        // 将resource/log-standard.md文件放在响应体中返回
        try {
            // 构建文件路径
            Resource resource = resourceLoader.getResource("classpath:file-to-download.md");
            UrlResource urlResource = new UrlResource(resource.getURL());

            // 检查文件是否存在
            if (!urlResource.exists()) {
                return ResponseEntity.notFound().build();
            }

            GalaxyLogger.info(LogCategory.BUSINESS_LOG, "文件下载: {}", urlResource.getFilename());
            // 返回文件
            return ResponseEntity.ok()
                    .contentLength(urlResource.contentLength())
                    .header("Content-Type", MediaType.APPLICATION_OCTET_STREAM_VALUE)
                    .header("Content-Disposition", "attachment; filename=\"" + urlResource.getFilename() + "\"")
                    .body(urlResource);
        } catch (MalformedURLException e) {
            GalaxyLogger.error(LogCategory.EXCEPTION_LOG, "文件下载失败", e);
            return ResponseEntity.badRequest().build();
        } catch (IOException e) {
            GalaxyLogger.error(LogCategory.EXCEPTION_LOG, "文件下载失败", e);
            throw new RuntimeException(e);
        }
    }

    @PostMapping(value = "/request-body")
    public BaseResponse<LogDTO> requestBody(@RequestBody LogDTO logDTO) {
        GalaxyLogger.info(LogCategory.BUSINESS_LOG, "请求体: {}", logDTO);
        return new BaseResponse<>(new Meta(true, "0", "成功"), logDTO);
    }

    @PostMapping(value = "/query-string", params = {"level", "category"})
    public BaseResponse<LogDTO> logLevel(@RequestParam(value = "level") String level,
                                         @RequestParam(value = "category") String category,
                                         @RequestBody LogDTO logDTO) {
        if (category == null || level == null) {
            throw new IllegalArgumentException("参数不能为空");
        }

        if (category.contains("default")) {
            logWithDefaultCategory(level, logDTO.getData());
        } else {
            logWithCategory(level, category, logDTO.getData());
        }
        return new BaseResponse<>(new Meta(true, "0", "成功"), logDTO);
    }

    private void logWithCategory(String level, String category, String data) {
        LogLevel logLevel = LogLevel.valueOf(level.toUpperCase());
        LogCategory logCategory = LogCategory.valueOf(category);
        String message = String.format("日志级别: %s, 日志类别: %s, 日志信息: %s", logLevel.name(), logCategory.name(), data);
        switch (logLevel) {
            case DEBUG:
                GalaxyLogger.debug(logCategory, message);
                break;
            case INFO:
                GalaxyLogger.info(logCategory, message);
                break;
            case WARN:
                GalaxyLogger.warn(logCategory, message);
                break;
            case ERROR:
                GalaxyLogger.error(logCategory, message);
                break;
            default:
                throw new IllegalArgumentException("日志级别不合法");
        }
    }

    private void logWithDefaultCategory(String level, String data) {
        LogLevel logLevel = LogLevel.valueOf(level.toUpperCase());
        String message = String.format("日志级别: %s, 日志信息: %s", logLevel.name(), data);
        switch (logLevel) {
            case DEBUG:
                GalaxyLogger.debug(message);
                break;
            case INFO:
                GalaxyLogger.info(message);
                break;
            case WARN:
                GalaxyLogger.warn(message);
                break;
            case ERROR:
                GalaxyLogger.error(message);
                break;
            default:
                throw new IllegalArgumentException("日志级别不合法");
        }
    }
}
