package cn.com.chinastock.cnf.feign.examples.controller;

import cn.com.chinastock.cnf.feign.exception.GalaxyFeignException;
import cn.com.chinastock.cnf.core.http.BaseResponse;
import cn.com.chinastock.cnf.core.http.Meta;
import cn.com.chinastock.cnf.feign.examples.client.ExceptionClient;
import cn.com.chinastock.cnf.feign.examples.client.LogClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/test/feign")
public class FeignController {

    @Autowired
    private LogClient logClient;

    @Autowired
    private ExceptionClient exceptionClient;

    @GetMapping("/log")
    public BaseResponse<Object> callLog() {
        String data = logClient.gzipResponse(10);
        return new BaseResponse<>(new Meta(true, "0", "success"), data);
    }

    @GetMapping("/not-found")
    public BaseResponse<Object> callNotFoundAPI() {
        try {
            logClient.notFound();
        } catch (GalaxyFeignException e) {
            return new BaseResponse<>(e.getMeta(), null);
        }
        return new BaseResponse<>(new Meta(true, "0", "success"), null);
    }

    @GetMapping("/forbidden")
    public BaseResponse<Object> callException() {
        try {
            exceptionClient.forbidden();
        } catch (GalaxyFeignException e) {
            return new BaseResponse<>(e.getMeta(), null);
        }
        return new BaseResponse<>(new Meta(true, "0", "success"), null);
    }
}
