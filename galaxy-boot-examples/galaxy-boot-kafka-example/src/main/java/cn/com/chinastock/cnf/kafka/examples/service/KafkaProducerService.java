package cn.com.chinastock.cnf.kafka.examples.service;

import cn.com.chinastock.cnf.core.log.GalaxyLoggerFactory;
import cn.com.chinastock.cnf.core.log.IGalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import cn.com.chinastock.cnf.kafka.examples.dto.BatchMessageRequest;
import cn.com.chinastock.cnf.kafka.examples.dto.MessageRequest;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Service;

import org.springframework.util.concurrent.ListenableFuture;
import org.springframework.util.concurrent.ListenableFutureCallback;

import java.util.ArrayList;
import java.util.List;

/**
 * Kafka消息生产者服务
 */
@Service
public class KafkaProducerService {

    private static final IGalaxyLogger logger = GalaxyLoggerFactory.getLogger(KafkaProducerService.class);

    private final KafkaTemplate<String, String> kafkaTemplateForString;

    private final KafkaTemplate<Integer, String> kafkaTemplate;

    public KafkaProducerService(KafkaTemplate<String, String> kafkaTemplateForString, KafkaTemplate<Integer, String> kafkaTemplate) {
        this.kafkaTemplateForString = kafkaTemplateForString;
        this.kafkaTemplate = kafkaTemplate;
    }

    /**
     * 发送单个消息（使用String key）
     * @param request 消息发送请求
     * @return 发送结果的CompletableFuture
     */
    public ListenableFuture<SendResult<String, String>> sendMessage(MessageRequest request) {
        logger.info(LogCategory.APP_LOG, "Sending message to topic: {}, key: {}, message: {}",
                   request.getTopic(), request.getKey(), request.getMessage());

        ListenableFuture<SendResult<String, String>> kafkaFuture =
            kafkaTemplateForString.send(request.getTopic(), request.getKey(), request.getMessage());

        kafkaFuture.addCallback(new ListenableFutureCallback<SendResult<String, String>>() {
            @Override
            public void onSuccess(SendResult<String, String> result) {
                logger.info(LogCategory.APP_LOG, "Message sent successfully to topic: {}, partition: {}, offset: {}",
                           result.getRecordMetadata().topic(),
                           result.getRecordMetadata().partition(),
                           result.getRecordMetadata().offset());
            }

            @Override
            public void onFailure(Throwable ex) {
                logger.error(LogCategory.EXCEPTION_LOG, "Failed to send message to topic: " + request.getTopic(), ex);
            }
        });

        return kafkaFuture;
    }

    /**
     * 发送单个消息（使用Integer key）
     * @param topic 主题名称
     * @param key 消息键（整数类型）
     * @param message 消息内容
     * @return 发送结果的ListenableFuture
     */
    public ListenableFuture<SendResult<Integer, String>> sendMessageWithIntegerKey(String topic, Integer key, String message) {
        logger.info(LogCategory.APP_LOG, "Sending message to topic: {}, key: {}, message: {}",
                   topic, key, message);

        ListenableFuture<SendResult<Integer, String>> kafkaFuture =
            kafkaTemplate.send(topic, key, message);

        kafkaFuture.addCallback(new ListenableFutureCallback<SendResult<Integer, String>>() {
            @Override
            public void onSuccess(SendResult<Integer, String> result) {
                logger.info(LogCategory.APP_LOG, "Message sent successfully to topic: {}, partition: {}, offset: {}",
                           result.getRecordMetadata().topic(),
                           result.getRecordMetadata().partition(),
                           result.getRecordMetadata().offset());
            }

            @Override
            public void onFailure(Throwable ex) {
                logger.error(LogCategory.EXCEPTION_LOG, "Failed to send message to topic: " + topic, ex);
            }
        });

        return kafkaFuture;
    }

    /**
     * 批量发送消息
     * @param request 批量消息发送请求
     * @return 发送结果的ListenableFuture列表
     */
    public List<ListenableFuture<SendResult<String, String>>> sendBatchMessages(BatchMessageRequest request) {
        logger.info(LogCategory.APP_LOG, "Sending batch messages to topic: {}, count: {}",
                   request.getTopic(), request.getMessages().size());

        List<ListenableFuture<SendResult<String, String>>> futures = new ArrayList<>();

        for (BatchMessageRequest.MessageItem item : request.getMessages()) {
            MessageRequest messageRequest = new MessageRequest(request.getTopic(), item.getKey(), item.getMessage());
            ListenableFuture<SendResult<String, String>> future = sendMessage(messageRequest);
            futures.add(future);
        }

        return futures;
    }

    /**
     * 发送消息到指定分区
     * @param topic 主题名称
     * @param partition 分区号
     * @param key 消息键
     * @param message 消息内容
     * @return 发送结果的ListenableFuture
     */
    public ListenableFuture<SendResult<String, String>> sendMessageToPartition(String topic, Integer partition, String key, String message) {
        logger.info(LogCategory.APP_LOG, "Sending message to topic: {}, partition: {}, key: {}, message: {}",
                   topic, partition, key, message);

        ListenableFuture<SendResult<String, String>> kafkaFuture =
            kafkaTemplateForString.send(topic, partition, key, message);

        kafkaFuture.addCallback(new ListenableFutureCallback<SendResult<String, String>>() {
            @Override
            public void onSuccess(SendResult<String, String> result) {
                logger.info(LogCategory.APP_LOG, "Message sent successfully to topic: {}, partition: {}, offset: {}",
                           result.getRecordMetadata().topic(),
                           result.getRecordMetadata().partition(),
                           result.getRecordMetadata().offset());
            }

            @Override
            public void onFailure(Throwable ex) {
                logger.error(LogCategory.EXCEPTION_LOG, "Failed to send message to topic: " + topic, ex);
            }
        });

        return kafkaFuture;
    }
}
