<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>cn.com.chinastock</groupId>
        <artifactId>galaxy-boot-examples</artifactId>
        <version>${revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>galaxy-boot-kafka-example</artifactId>
    <packaging>jar</packaging>
    <name>Galaxy Boot Kafka Example</name>

    <dependencies>
        <dependency>
            <groupId>cn.com.chinastock</groupId>
            <artifactId>galaxy-boot-starter-web-jdk8</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>cn.com.chinastock</groupId>
            <artifactId>galaxy-boot-starter-kafka-jdk8</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>cn.com.chinastock</groupId>
            <artifactId>galaxy-boot-starter-metrics-jdk8</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- Apollo Client for dynamic configuration -->
        <dependency>
            <groupId>com.ctrip.framework.apollo</groupId>
            <artifactId>apollo-client</artifactId>
        </dependency>
    </dependencies>
    
    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
