package cn.com.chinastock.cnf.fastjson;

import com.alibaba.fastjson2.support.config.FastJsonConfig;
import com.alibaba.fastjson2.support.spring.http.converter.FastJsonHttpMessageConverter;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.mock.http.MockHttpOutputMessage;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter;

import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

@AutoConfigureMockMvc
@EnableAutoConfiguration
@SpringBootTest(classes = {GalaxyFastJsonConfig.class, TestController.class},
        webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class GalaxyFastJsonConfigTest {
    @Autowired
    private TestRestTemplate restTemplate;

    @Autowired
    private RequestMappingHandlerAdapter handlerAdapter;

    @Autowired
    private FastJsonConfig fastJsonConfig;

    @Test
    public void testFastJsonHttpMessageConverter() throws Exception {
        GalaxyFastJsonConfig config = new GalaxyFastJsonConfig(new FastJsonProperties());
        FastJsonHttpMessageConverter converter = config.fastJsonHttpMessageConverter(fastJsonConfig);

        // 验证字符集
        assertEquals(StandardCharsets.UTF_8, converter.getFastJsonConfig().getCharset());

        // 验证 MediaType
        List<MediaType> mediaTypes = converter.getSupportedMediaTypes();
        assertTrue(mediaTypes.contains(MediaType.APPLICATION_JSON_UTF8));

        // 测试转换
        MockHttpOutputMessage outputMessage = new MockHttpOutputMessage();
        converter.write("测试", MediaType.APPLICATION_JSON_UTF8, outputMessage);
        String jsonOutput = outputMessage.getBodyAsString();
        assertEquals("\"测试\"", jsonOutput);
    }

    @Test
    public void shouldSerializeAndDeserializeCorrectly() {
        TestModel testModel = new TestModel();
        testModel.setName("测试中文");
        testModel.setAmount(new BigDecimal("1234.5600"));
        testModel.setTransientField("不应该被序列化");

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
        HttpEntity<TestModel> request = new HttpEntity<>(testModel, headers);
        ResponseEntity<TestModel> response = restTemplate.postForEntity("/test", request, TestModel.class);

        assertThat(response.getStatusCode().is2xxSuccessful()).isTrue();
        TestModel responseModel = response.getBody();
        assertThat(responseModel).isNotNull();
        assertThat(responseModel.getName()).isEqualTo("测试中文");
        assertThat(responseModel.getAmount()).isEqualTo("1234.5600");
        assertThat(responseModel.getAmount()).isEqualTo(new BigDecimal("1234.5600"));
        assertThat(responseModel.getTransientField()).isNull();
    }

    @Test
    public void testFastJsonIsUsed() {
        List<HttpMessageConverter<?>> converters = handlerAdapter.getMessageConverters();
        boolean fastJsonPresent = converters.stream()
                .anyMatch(converter -> converter instanceof FastJsonHttpMessageConverter);
        assertTrue(fastJsonPresent, "FastJsonHttpMessageConverter 应该被使用");
    }
}
