package cn.com.chinastock.cnf.metrics;

import cn.com.chinastock.cnf.core.log.GalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import com.zaxxer.hikari.HikariDataSource;
import io.micrometer.core.instrument.Clock;
import io.micrometer.core.instrument.Meter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.config.MeterFilter;
import io.micrometer.core.instrument.config.MeterFilterReply;
import io.micrometer.core.instrument.logging.LoggingMeterRegistry;
import io.micrometer.core.instrument.logging.LoggingRegistryConfig;
import io.micrometer.prometheus.PrometheusMeterRegistry;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.lookup.AbstractRoutingDataSource;

import javax.annotation.PostConstruct;
import javax.sql.DataSource;
import java.time.Duration;

/**
 * 配置 Hikari 数据源的监控指标，通过日志和 Prometheus 输出。
 */
@Configuration
@EnableConfigurationProperties(GalaxyMetricsProperties.class)
@ConditionalOnClass({HikariDataSource.class})
public class GalaxyHikariMetricAutoConfiguration {
    @Autowired
    private DataSource dataSource;

    @Autowired
    private GalaxyMetricsProperties properties;

    /**
     * 注册 LoggingMeterRegistry，用于记录 Hikari 数据源的指标日志
     *
     * @return LoggingMeterRegistry
     */
    @Bean
    @ConditionalOnProperty(prefix = GalaxyMetricsProperties.CONFIG_PREFIX, name = "datasource.logging.enable", havingValue = "true")
    public MeterRegistry loggingMeterRegistry() {
        LoggingMeterRegistry loggingMeterRegistry = getLoggingMeterRegistry();
        loggingMeterRegistry.config().meterFilter(new MeterFilter() {
            @Override
            public MeterFilterReply accept(Meter.Id id) {
                return id.getName().startsWith("hikaricp") ? MeterFilterReply.ACCEPT : MeterFilterReply.DENY;
            }
        });

        // 确保数据源是 HikariDataSource 类型，并注册指标
        if (dataSource instanceof HikariDataSource) {
            HikariDataSource hikariDataSource = (HikariDataSource) dataSource;
            hikariDataSource.setMetricRegistry(loggingMeterRegistry);

            GalaxyLogger.info(LogCategory.FRAMEWORK_LOG, "MetricRegistry: " + hikariDataSource.getMetricRegistry());
        }

        // 如果数据源是动态数据源，默认的 dataSource 将是个 AbstractRoutingDataSource 类型，此时需要获取默认数据源
        if (dataSource.getClass().getName().equals("cn.com.chinastock.cnf.oceanbase.GalaxyDynamicDataSource") && dataSource instanceof AbstractRoutingDataSource) {
            DataSource defaultSource = ((AbstractRoutingDataSource) dataSource).getResolvedDefaultDataSource();
            if (defaultSource instanceof HikariDataSource) {
                HikariDataSource hikariDataSource = (HikariDataSource) defaultSource;
                hikariDataSource.setMetricRegistry(loggingMeterRegistry);

                GalaxyLogger.info(LogCategory.FRAMEWORK_LOG, "MetricRegistry: " + hikariDataSource.getMetricRegistry());
            }
        }

        return loggingMeterRegistry;
    }

    /**
     * 注册 PrometheusMeterRegistry，用于记录 Hikari 数据源的指标到 Prometheus
     *
     * @param prometheusMeterRegistry PrometheusMeterRegistry
     * @return PrometheusMeterRegistry
     */
    @Bean(name = "customPrometheusMeterRegistry")
    @ConditionalOnProperty(prefix = GalaxyMetricsProperties.CONFIG_PREFIX, name = "datasource.prometheus.enable", havingValue = "true")
    @ConditionalOnClass({PrometheusMeterRegistry.class})
    public MeterRegistry customPrometheusMeterRegistry(PrometheusMeterRegistry prometheusMeterRegistry) {
        if (dataSource instanceof HikariDataSource) {
            HikariDataSource hikariDataSource = (HikariDataSource) dataSource;
            if (hikariDataSource.getMetricRegistry() == null) {
                hikariDataSource.setMetricRegistry(prometheusMeterRegistry);
            }
            GalaxyLogger.info(LogCategory.FRAMEWORK_LOG, "MetricRegistry: " + hikariDataSource.getMetricRegistry());
            return prometheusMeterRegistry;
        }

        // 如果数据源是动态数据源，默认的 dataSource 将是个 AbstractRoutingDataSource 类型，此时需要获取默认数据源
        if (dataSource.getClass().getName().equals("cn.com.chinastock.cnf.oceanbase.GalaxyDynamicDataSource") && dataSource instanceof AbstractRoutingDataSource) {
            AbstractRoutingDataSource routingDataSource = (AbstractRoutingDataSource) dataSource;
            DataSource defaultSource = routingDataSource.getResolvedDefaultDataSource();
            if (defaultSource instanceof HikariDataSource) {
                HikariDataSource hikariDataSource = (HikariDataSource) defaultSource;
                if (hikariDataSource.getMetricRegistry() == null) {
                    hikariDataSource.setMetricRegistry(prometheusMeterRegistry);
                }
                GalaxyLogger.info(LogCategory.FRAMEWORK_LOG, "MetricRegistry: " + hikariDataSource.getMetricRegistry());
            }
        }

        return prometheusMeterRegistry;
    }

    private LoggingMeterRegistry getLoggingMeterRegistry() {
        LoggingRegistryConfig config = new LoggingRegistryConfig() {
            @Override
            public String get(String key) {
                return null;
            }

            @Override
            public Duration step() {
                return Duration.ofSeconds(properties.getDatasource().getLogging().getInterval());
            }
        };

        return new LoggingMeterRegistry(config, Clock.SYSTEM);
    }

    @PostConstruct
    public void init() {
        if (dataSource instanceof HikariDataSource) {
            HikariDataSource hikariDataSource = (HikariDataSource) dataSource;
        }
    }
}
