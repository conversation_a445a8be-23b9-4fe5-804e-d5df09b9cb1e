<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>cn.com.chinastock</groupId>
        <artifactId>galaxy-boot-jdk8</artifactId>
        <version>${revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>galaxy-boot-starters-jdk8</artifactId>
    <packaging>pom</packaging>
    <name>Galaxy Boot Starters</name>
    <description>Galaxy Boot Starters</description>

    <modules>
        <module>galaxy-boot-starter-web</module>
        <module>galaxy-boot-starter-tongweb</module>
        <module>galaxy-boot-starter-data-oceanbase</module>
        <module>galaxy-boot-starter-datasource</module>
        <module>galaxy-boot-starter-metrics</module>
        <module>galaxy-boot-starter-feign</module>
        <module>galaxy-boot-starter-fastjson</module>
        <module>galaxy-boot-starter-swagger</module>
        <module>galaxy-boot-starter-kafka</module>
    </modules>

    <build>
        <plugins>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>${jacoco.version}</version>
                <executions>
                    <execution>
                        <id>jacoco-initialize</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>jacoco-site</id>
                        <phase>test</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>