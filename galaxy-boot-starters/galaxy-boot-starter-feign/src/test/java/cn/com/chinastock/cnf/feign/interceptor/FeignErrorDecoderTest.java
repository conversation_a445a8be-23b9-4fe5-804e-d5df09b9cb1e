package cn.com.chinastock.cnf.feign.interceptor;

import cn.com.chinastock.cnf.feign.exception.GalaxyFeignException;
import cn.com.chinastock.cnf.core.http.Meta;
import feign.Request;
import feign.Response;
import feign.FeignException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import feign.RetryableException;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.HashMap;
import java.util.Collection;

import static org.junit.jupiter.api.Assertions.*;

public class FeignErrorDecoderTest {

    private FeignErrorDecoder errorDecoder;
    private static final String METHOD_KEY = "TestMethod";

    @BeforeEach
    void setUp() {
        errorDecoder = new FeignErrorDecoder();
    }

    @Test
    void shouldDecodeErrorResponse() {
        // Given
        String errorBody = "{\"meta\":{\"success\":false,\"code\":\"EXPTCNF401\",\"message\":\"用户未认证\"},\"data\":null}";
        HashMap<String, Collection<String>> headers = new HashMap<>();
        headers.put("Content-Type", Collections.singletonList("application/json"));
        headers.put("X-Request-ID", Collections.singletonList("test-request-id"));
        
        Response response = Response.builder()
                .status(500)
                .reason("Internal Server Error")
                .headers(headers)
                .request(Request.create(Request.HttpMethod.GET, "/test", 
                        Collections.emptyMap(), null, StandardCharsets.UTF_8, null))
                .body(errorBody, StandardCharsets.UTF_8)
                .build();

        // When
        Exception exception = errorDecoder.decode(METHOD_KEY, response);

        // Then
        assertInstanceOf(GalaxyFeignException.class, exception);
        GalaxyFeignException galaxyFeignException = (GalaxyFeignException) exception;
        
        // 验证 GalaxyFeignException 特有的属性
        Meta meta = galaxyFeignException.getMeta();
        assertEquals("EXPTCNF401", meta.getCode());
        assertEquals("用户未认证", meta.getMessage());
        assertFalse(meta.isSuccess());
        
        // 验证继承自 FeignException 的属性
        assertEquals(500, galaxyFeignException.status());
        assertTrue(galaxyFeignException.getMessage().contains("Internal Server Error"));
        
        // 验证响应体
        assertArrayEquals(errorBody.getBytes(StandardCharsets.UTF_8), galaxyFeignException.responseBody().get().array());
        String actualBody = new String(galaxyFeignException.responseBody().get().array(), StandardCharsets.UTF_8);
        assertEquals(errorBody, actualBody);
        
        // 验证请求信息
        assertEquals("/test", galaxyFeignException.request().url());
        assertEquals(Request.HttpMethod.GET, galaxyFeignException.request().httpMethod());
        
        // 验证响应头
        assertEquals("application/json", galaxyFeignException.responseHeaders().get("Content-Type").iterator().next());
        assertEquals("test-request-id", galaxyFeignException.responseHeaders().get("X-Request-ID").iterator().next());
    }

    @ParameterizedTest
    @ValueSource(strings = {"Invalid JSON Content", "", "{\"test\":null}"})
    void shouldFallbackToDefaultDecoderOnInvalidJson(String requestBody) {
        // Given
        String invalidJson = "Invalid JSON Content";
        Response response = Response.builder()
                .status(500)
                .reason("Internal Server Error")
                .headers(new HashMap<>())
                .request(Request.create(Request.HttpMethod.GET, "/test", 
                        Collections.emptyMap(), null, StandardCharsets.UTF_8, null))
                .body(invalidJson, StandardCharsets.UTF_8)
                .build();

        // When
        Exception exception = errorDecoder.decode(METHOD_KEY, response);

        // Then
        assertFalse(exception instanceof GalaxyFeignException);
        assertInstanceOf(FeignException.class, exception);
        FeignException feignException = (FeignException) exception;
        assertEquals(500, feignException.status());
        String expectedMessage = "[500 Internal Server Error] during [GET] to [/test] [TestMethod]: [Invalid JSON Content]";
        assertEquals(expectedMessage, feignException.getMessage());
    }

    @Test
    void shouldReturnRetryableExceptionOnNetworkError() {
        // Given
        HashMap<String, Collection<String>> headers = new HashMap<>();
        headers.put("Retry-After", Collections.singletonList("120")); // 添加 Retry-After header
        
        Response response = Response.builder()
                .status(503)  // Service Unavailable
                .reason("Service Temporarily Unavailable")
                .headers(headers)
                .request(Request.create(Request.HttpMethod.GET, "/test", 
                        Collections.emptyMap(), null, StandardCharsets.UTF_8, null))
                .body("Service is temporarily unavailable", StandardCharsets.UTF_8)
                .build();

        // When
        Exception exception = errorDecoder.decode(METHOD_KEY, response);

        // Then
        assertInstanceOf(RetryableException.class, exception);
        RetryableException retryableException = (RetryableException) exception;
        assertEquals(503, retryableException.status());
        assertTrue(retryableException.getMessage().contains("Service Temporarily Unavailable"));
    }
} 