package cn.com.chinastock.cnf.feign.config;

import cn.com.chinastock.cnf.core.log.context.ITraceContext;
import cn.com.chinastock.cnf.feign.esb.ESBProperties;
import cn.com.chinastock.cnf.feign.interceptor.FeignErrorDecoder;
import cn.com.chinastock.cnf.feign.interceptor.FeignRequestInterceptor;
import cn.com.chinastock.cnf.feign.okhttp.OkHttpClientProperties;
import feign.Client;
import feign.Logger;
import feign.Request;
import feign.RequestInterceptor;
import feign.codec.ErrorDecoder;
import okhttp3.ConnectionPool;
import okhttp3.OkHttpClient;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;

import java.util.concurrent.TimeUnit;

import static org.assertj.core.api.Assertions.assertThat;

@SpringBootTest(classes = {
        GalaxyBootFeignAutoConfiguration.class,
        GalaxyBootFeignLoadBalancerAutoConfiguration.class
})
@EnableAutoConfiguration
@ActiveProfiles("test")
@TestPropertySource(properties = {
        "spring.cloud.openfeign.client.config.default.connectTimeout=999",
        "spring.cloud.openfeign.client.config.default.readTimeout=1999",
        "spring.cloud.openfeign.client.config.default.writeTimeout=2999",
        "spring.cloud.openfeign.httpclient.maxConnections=99",
        "spring.cloud.openfeign.httpclient.timeToLive=199",
        "spring.cloud.openfeign.httpclient.timeToLiveUnit=SECONDS",
        "spring.cloud.openfeign.client.config.default.loggerLevel=FULL"
})
class GalaxyBootFeignAutoConfigurationIntegrationTest {

    @MockBean
    ITraceContext traceContext;

    @MockBean
    ESBProperties esbProperties;

    @Autowired
    private OkHttpClientProperties okHttpClientProperties;

    @Autowired
    private OkHttpClient okHttpClient;

    @Autowired
    private ConnectionPool connectionPool;

    @Autowired
    private Client feignClient;

    @Autowired
    private Request.Options requestOptions;

    @Autowired
    private RequestInterceptor requestInterceptor;

    @Autowired
    private ErrorDecoder errorDecoder;

    @Autowired
    private Logger.Level loggerLevel;


    @Nested
    class PropertiesTests {
        @Test
        void shouldLoadCustomProperties() {
            assertThat(okHttpClientProperties)
                    .satisfies(props -> {
                        assertThat(props.getMaxConnections()).isEqualTo(99);
                        assertThat(props.getTimeToLive()).isEqualTo(199);
                        assertThat(props.getTimeToLiveUnit()).isEqualTo(TimeUnit.SECONDS);
                        assertThat(props.getConnectTimeout()).isEqualTo(999);
                        assertThat(props.getReadTimeout()).isEqualTo(1999);
                        assertThat(props.getWriteTimeout()).isEqualTo(2999);
                        assertThat(props.getLoggerLevel()).isEqualTo(Logger.Level.FULL);
                    });
        }
    }

    @Nested
    class OkHttpClientTests {
        @Test
        void shouldConfigureOkHttpClientCorrectly() {
            assertThat(okHttpClient)
                    .isNotNull()
                    .satisfies(client -> {
                        assertThat(client.connectTimeoutMillis()).isEqualTo(999);
                        assertThat(client.readTimeoutMillis()).isEqualTo(1999);
                        assertThat(client.writeTimeoutMillis()).isEqualTo(2999);
                        assertThat(client.connectionPool()).isSameAs(connectionPool);
                    });
        }

        @Test
        void shouldConfigureConnectionPool() {
            assertThat(connectionPool)
                    .isNotNull()
                    .satisfies(pool -> {
                        assertThat(pool.idleConnectionCount()).isZero();
                        assertThat(pool.connectionCount()).isZero();
                    });
        }
    }

    @Nested
    class FeignConfigurationTests {

        @Test
        void shouldConfigureRequestOptions() {
            assertThat(requestOptions)
                    .isNotNull()
                    .satisfies(options -> {
                        assertThat(options.connectTimeoutMillis()).isEqualTo(999);
                        assertThat(options.readTimeoutMillis()).isEqualTo(1999);
                    });
        }

        @Test
        void shouldConfigureDefaultFeignClient() {
            // Only run this test if LoadBalancerClient is not on classpath
            if (!isLoadBalancerPresent()) {
                assertThat(feignClient)
                        .isNotNull()
                        .isInstanceOf(feign.okhttp.OkHttpClient.class)
                        .hasFieldOrPropertyWithValue("delegate", okHttpClient);
            }
        }

        private boolean isLoadBalancerPresent() {
            try {
                Class.forName("org.springframework.cloud.client.loadbalancer.LoadBalancerClient");
                return true;
            } catch (ClassNotFoundException e) {
                return false;
            }
        }

        @Test
        void shouldConfigureRequestInterceptor() {
            assertThat(requestInterceptor)
                    .isNotNull()
                    .isInstanceOf(FeignRequestInterceptor.class);
        }

        @Test
        void shouldConfigureErrorDecoder() {
            assertThat(errorDecoder)
                    .isNotNull()
                    .isInstanceOf(FeignErrorDecoder.class);
        }

        @Test
        void shouldConfigureLoggerLevel() {
            assertThat(loggerLevel).isEqualTo(Logger.Level.FULL);
        }
    }
}
