package cn.com.chinastock.cnf.feign.config;

import cn.com.chinastock.cnf.core.log.GalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import cn.com.chinastock.cnf.core.log.context.ITraceContext;
import cn.com.chinastock.cnf.feign.esb.ESBProperties;
import cn.com.chinastock.cnf.feign.interceptor.FeignErrorDecoder;
import cn.com.chinastock.cnf.feign.interceptor.FeignRequestInterceptor;
import cn.com.chinastock.cnf.feign.okhttp.OkHttpClientProperties;
import com.alibaba.fastjson2.support.spring.http.converter.FastJsonHttpMessageConverter;
import feign.Client;
import feign.Logger;
import feign.codec.Decoder;
import feign.codec.Encoder;
import org.springframework.boot.autoconfigure.AutoConfigureBefore;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.openfeign.FeignAutoConfiguration;
import org.springframework.cloud.openfeign.support.SpringDecoder;
import org.springframework.cloud.openfeign.support.SpringEncoder;
import org.springframework.context.annotation.Bean;

import java.util.Optional;

@AutoConfigureBefore(FeignAutoConfiguration.class)
@EnableConfigurationProperties(ESBProperties.class)
public class GalaxyBootFeignAutoConfiguration {

    @Bean
    @ConditionalOnMissingBean
    public FeignRequestInterceptor feignRequestInterceptor(ITraceContext traceContext, ESBProperties esbProperties) {
        GalaxyLogger.info(LogCategory.FRAMEWORK_LOG, "GalaxyBoot: Initializing FeignRequestInterceptor");
        return new FeignRequestInterceptor(traceContext, esbProperties);
    }

    @Bean
    @ConditionalOnMissingBean
    public FeignErrorDecoder galaxyErrorDecoder() {
        GalaxyLogger.info(LogCategory.FRAMEWORK_LOG, "GalaxyBoot: Initializing FeignErrorDecoder");
        return new FeignErrorDecoder();
    }

    @Bean
    @ConditionalOnMissingBean(Client.class)
    public Client defaultFeignClient(okhttp3.OkHttpClient client) {
        GalaxyLogger.info(LogCategory.FRAMEWORK_LOG, "GalaxyBoot: Initializing default Feign OkHttpClient");
        return new feign.okhttp.OkHttpClient(client);
    }

    @Bean
    public Logger.Level feignLoggerLevel(OkHttpClientProperties okHttpClientProperties) {
        Logger.Level loggerLevel = Optional.ofNullable(okHttpClientProperties.getLoggerLevel())
                .orElse(Logger.Level.NONE);
        GalaxyLogger.info(LogCategory.FRAMEWORK_LOG, "GalaxyBoot: Initializing Feign logger level: {}", loggerLevel);
        return loggerLevel;
    }

    @ConditionalOnBean(FastJsonHttpMessageConverter.class)
    @Bean
    public Encoder feignEncoder(FastJsonHttpMessageConverter fastJsonHttpMessageConverter) {
        GalaxyLogger.info(LogCategory.FRAMEWORK_LOG, "GalaxyBoot: Initializing Feign Encoder with FastJsonHttpMessageConverter");
        return new SpringEncoder(() -> new HttpMessageConverters(fastJsonHttpMessageConverter));
    }

    @ConditionalOnBean(FastJsonHttpMessageConverter.class)
    @Bean
    public Decoder feignDecoder(FastJsonHttpMessageConverter fastJsonHttpMessageConverter) {
        GalaxyLogger.info(LogCategory.FRAMEWORK_LOG, "GalaxyBoot: Initializing Feign Decoder with FastJsonHttpMessageConverter");
        return new SpringDecoder(() -> new HttpMessageConverters(fastJsonHttpMessageConverter));
    }
}
