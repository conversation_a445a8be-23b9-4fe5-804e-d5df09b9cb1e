package cn.com.chinastock.cnf.feign.interceptor;

import cn.com.chinastock.cnf.feign.exception.GalaxyFeignException;
import cn.com.chinastock.cnf.core.http.BaseResponse;
import cn.com.chinastock.cnf.core.log.GalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import com.alibaba.fastjson2.JSON;
import feign.Response;
import feign.codec.ErrorDecoder;
import org.apache.commons.io.IOUtils;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

@Component
public class FeignErrorDecoder implements ErrorDecoder {

    private final ErrorDecoder defaultDecoder = new Default();

    @Override
    public Exception decode(String methodKey, Response response) {
        try {
            if (response.body() == null) {
                return defaultDecoder.decode(methodKey, response);
            }
            
            String body = IOUtils.toString(response.body().asInputStream(), StandardCharsets.UTF_8);
            if (!JSON.isValid(body)) {
                return defaultDecoder.decode(methodKey, response);
            }
            
            BaseResponse<Object> responseDTO = JSON.parseObject(body, BaseResponse.class);
            if (responseDTO == null || responseDTO.getMeta() == null) {
                return defaultDecoder.decode(methodKey, response);
            }
            GalaxyLogger.debug(LogCategory.EXCEPTION_LOG, "Feign调用异常: {} {}", responseDTO.getMeta(), methodKey);
            return new GalaxyFeignException(response.status(), response.reason(), response.request(),
                    body.getBytes(StandardCharsets.UTF_8), response.headers(), responseDTO.getMeta());
        } catch (Exception ex) {
            return defaultDecoder.decode(methodKey, response);
        }
    }
}
