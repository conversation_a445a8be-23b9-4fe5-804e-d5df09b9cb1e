package cn.com.chinastock.cnf.datasource;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties(prefix = GalaxyDataSourceProperties.CONFIG_PREFIX)
public class GalaxyDataSourceProperties {
    public static final String CONFIG_PREFIX = "galaxy.datasource";
    private boolean dynamicMaximumPoolSize = false;
    private boolean dynamicRefreshUsernamePassword = false;

    public boolean isDynamicMaximumPoolSize() {
        return dynamicMaximumPoolSize;
    }

    public void setDynamicMaximumPoolSize(boolean dynamicMaximumPoolSize) {
        this.dynamicMaximumPoolSize = dynamicMaximumPoolSize;
    }

    public boolean isDynamicRefreshUsernamePassword() {
        return dynamicRefreshUsernamePassword;
    }

    public void setDynamicRefreshUsernamePassword(boolean dynamicRefreshUsernamePassword) {
        this.dynamicRefreshUsernamePassword = dynamicRefreshUsernamePassword;
    }
}