package cn.com.chinastock.cnf.core.log.logger;

import cn.com.chinastock.cnf.core.http.BaseResponse;
import cn.com.chinastock.cnf.core.log.GalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import cn.com.chinastock.cnf.core.log.config.LogProperties;
import cn.com.chinastock.cnf.core.log.utils.HttpFileUtils;
import cn.com.chinastock.cnf.core.log.utils.HttpLogUtils;
import com.alibaba.fastjson2.JSON;

import javax.servlet.http.HttpServletResponse;

import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.web.util.ContentCachingResponseWrapper;

import java.util.HashMap;
import java.util.Map;

import static cn.com.chinastock.cnf.core.log.utils.HttpLogUtils.formatLogInfo;
import static cn.com.chinastock.cnf.core.log.utils.HttpLogUtils.maskSensitiveFields;
import static com.alibaba.fastjson2.JSONPath.extract;

/**
 * ResponseLogger 类用于记录 HTTP 响应的日志信息。
 *
 * <p>该类中包含以下公共方法：</p>
 *
 * <ul>
 *     <li>{@link #ResponseLogger(LogProperties)}：构造函数，初始化日志配置属性。</li>
 *     <li>{@link #log(HttpServletResponse)}：记录 HTTP 响应的日志信息。</li>
 *     <li>{@link #logWithFieldsMasked(MediaType, HttpHeaders, int, Object)}：记录 HTTP 响应的日志信息，对响应体中的字段进行掩码处理。</li>
 * </ul>
 *
 * <AUTHOR>
 */
public class ResponseLogger {
    private final LogProperties logProperties;

    /**
     * 构造函数
     *
     * @param logProperties 日志配置属性
     */
    public ResponseLogger(LogProperties logProperties) {
        this.logProperties = logProperties;
    }

    /**
     * 记录响应日志
     *
     * @param response HTTP响应对象
     */
    public void log(HttpServletResponse response) {
        try {
            Map<String, Object> responseInfo = buildResponseInfo(response);
            String logMessage = formatLogInfo(responseInfo);
            GalaxyLogger.info(LogCategory.RESPONSE_LOG, logMessage);
        } catch (Exception e) {
            GalaxyLogger.error(LogCategory.EXCEPTION_LOG, "Failed to log response", e);
        }
    }

    /**
     * 构建响应信息
     *
     * @param response HTTP响应对象
     * @return 包含响应信息的Map
     */
    private Map<String, Object> buildResponseInfo(HttpServletResponse response) {
        Map<String, Object> responseInfo = new HashMap<>();
        responseInfo.put("http_status_code", response.getStatus());
        responseInfo.put("meta_code", null);
        responseInfo.put("headers", getResponseHeaders(response));

        String body = getResponseBody(response);
        responseInfo.put("body", body);

        if (body != null &&
                response.getContentType() != null &&
                response.getContentType().contains(MediaType.APPLICATION_JSON_VALUE)) {
            String metaCode = extractMetaCode(body);
            if (metaCode != null) {
                responseInfo.put("meta_code", metaCode);
            }
        }

        return responseInfo;
    }

    /**
     * 获取响应头信息
     *
     * @param response HTTP响应对象
     * @return 响应头信息字符串，如果未启用则返回null
     */
    private String getResponseHeaders(HttpServletResponse response) {
        if (!logProperties.isResponseHeadersEnabled()) {
            return null;
        }

        Map<String, String> headers = new HashMap<>();
        for (String headerName : response.getHeaderNames()) {
            headers.put(headerName, response.getHeader(headerName));
        }
        return JSON.toJSONString(headers);
    }

    /**
     * 获取响应体内容
     *
     * @param response HTTP响应对象
     * @return 响应体内容，对于文件下载返回文件信息，其他返回响应体内容
     */
    private String getResponseBody(HttpServletResponse response) {
        String contentDisposition = response.getHeader(HttpHeaders.CONTENT_DISPOSITION);
        if (HttpLogUtils.isFileDownloadResponse(contentDisposition)) {
            return extractFileDownloadInfo(contentDisposition, response.getHeader(HttpHeaders.CONTENT_LENGTH));
        }

        if (!HttpLogUtils.isRecordableContentType(response.getContentType())) {
            return null;
        }

        if (!(response instanceof ContentCachingResponseWrapper)) {
            return null;
        }

        ContentCachingResponseWrapper wrapper = (ContentCachingResponseWrapper) response;
        return HttpLogUtils.extractBody(wrapper.getContentAsByteArray());
    }

    /**
     * 提取文件下载信息，以JSON格式返回
     * 格式为：{"file_name":"文件名","file_size":"文件大小"}
     *
     * @param headerContentDisposition Content-Disposition头的值
     * @param headerContentLength      Content-Length头的值
     * @return JSON格式的文件信息字符串
     */
    private String extractFileDownloadInfo(String headerContentDisposition, String headerContentLength) {
        String filename = HttpFileUtils.extractFilename(headerContentDisposition);
        if (filename == null) {
            return null;
        }

        Map<String, String> fileInfo = new HashMap<>();
        fileInfo.put("file_name", filename);
        fileInfo.put("file_size", HttpFileUtils.formatFileSize(HttpLogUtils.getContentLength(headerContentLength)));

        return JSON.toJSONString(fileInfo);
    }

    /**
     * 从响应体中提取meta.code
     *
     * @param body 响应体内容
     * @return meta.code值，如果不存在则返回null
     */
    private String extractMetaCode(String body) {
        try {
            Object metaCode = extract(body, "$.meta.code");
            return metaCode != null ? metaCode.toString() : null;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 记录JSON响应日志
     *
     * @param contentType  响应内容类型
     * @param headers      响应头信息
     * @param statusCode   HTTP状态码
     * @param responseBody 响应对象
     */
    public void logWithFieldsMasked(MediaType contentType, HttpHeaders headers, int statusCode, Object responseBody) {
        Map<String, Object> responseInfo = new HashMap<>();

        try {
            // 获取当前响应
            responseInfo.put("http_status_code", statusCode);
            responseInfo.put("meta_code", null);
            responseInfo.put("headers", logProperties.isResponseHeadersEnabled() ?
                    JSON.toJSONString(headers.toSingleValueMap()) : null);

            String body = getMaskedResponseBody(contentType, headers, responseBody);
            responseInfo.put("body", body);

            if (responseBody instanceof BaseResponse<?>) {
                BaseResponse<?> baseResponse = (BaseResponse<?>) responseBody;
                responseInfo.put("meta_code", baseResponse.getMeta().getCode());
            }

            // 记录日志
            String logMessage = formatLogInfo(responseInfo);
            GalaxyLogger.info(LogCategory.RESPONSE_LOG, logMessage);
        } catch (Exception e) {
            GalaxyLogger.error(LogCategory.EXCEPTION_LOG, "Failed to log response with fields masked", e);
        }
    }

    private String getMaskedResponseBody(MediaType contentType, HttpHeaders headers, Object responseBody) {
        String contentDisposition = headers.getFirst("Content-Disposition");
        if (HttpLogUtils.isFileDownloadResponse(contentDisposition)) {
            return extractFileDownloadInfo(contentDisposition, headers.getFirst("Content-Length"));
        }

        if (!HttpLogUtils.isRecordableContentType(contentType.toString())) {
            return null;
        }

        // 处理响应体
        if (responseBody != null) {
            return maskSensitiveFields(responseBody);
        }

        return null;
    }
}