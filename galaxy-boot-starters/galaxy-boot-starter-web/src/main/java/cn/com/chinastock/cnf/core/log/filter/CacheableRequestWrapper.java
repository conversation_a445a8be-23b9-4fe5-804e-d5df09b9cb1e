package cn.com.chinastock.cnf.core.log.filter;

import cn.com.chinastock.cnf.core.log.GalaxyLogger;
import cn.com.chinastock.cnf.core.log.utils.HttpLogUtils;
import javax.servlet.ReadListener;
import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import org.springframework.lang.NonNull;
import org.springframework.util.StreamUtils;

import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;

/**
 * CacheableRequestWrapper 类是一个可缓存的请求包装器，支持多次读取请求体内容。
 *
 * <p>该类中包含以下公共方法：</p>
 *
 * <ul>
 *     <li>{@link #getInputStream()}：获取缓存的输入流。</li>
 *     <li>{@link #getReader()}：获取缓存的读取器。</li>
 *     <li>{@link #getCachedBody()}：获取缓存的请求体。</li>
 *     <li>{@link #isCached()}：检查请求体是否已缓存。</li>
 * </ul>
 *
 * <AUTHOR>
 */
public class CacheableRequestWrapper extends HttpServletRequestWrapper {
    private static final int MAX_CACHE_SIZE = 1024 * 1024; // 1MB
    private final byte[] cachedBody;
    private final boolean cached;

    public CacheableRequestWrapper(HttpServletRequest request) throws IOException {
        super(request);

        // 只缓存特定类型的请求
        String contentType = request.getContentType();
        if (HttpLogUtils.isRecordableContentType(contentType)) {
            int contentLength = request.getContentLength();
            if (contentLength > MAX_CACHE_SIZE) {
                this.cached = false;
                this.cachedBody = null;
                GalaxyLogger.warn("Request body is too large to cache: {} bytes", contentLength);
            } else {
                this.cachedBody = StreamUtils.copyToByteArray(request.getInputStream());
                this.cached = true;
            }
        } else {
            this.cached = false;
            this.cachedBody = null;
        }
    }

    @Override
    public ServletInputStream getInputStream() throws IOException {
        if (cached) {
            return new CachedServletInputStream(this.cachedBody);
        }
        return getRequest().getInputStream();
    }

    @Override
    public BufferedReader getReader() throws IOException {
        if (cached) {
            String characterEncoding = getCharacterEncoding();
            Charset charset = (characterEncoding != null) ?
                    Charset.forName(characterEncoding) : StandardCharsets.UTF_8;
            return new BufferedReader(
                    new InputStreamReader(new ByteArrayInputStream(cachedBody), charset)
            );
        }
        return getRequest().getReader();
    }

    public byte[] getCachedBody() {
        return cached ? this.cachedBody : null;
    }

    public boolean isCached() {
        return cached;
    }

    private static class CachedServletInputStream extends ServletInputStream {
        private final ByteArrayInputStream buffer;

        public CachedServletInputStream(byte[] contents) {
            this.buffer = new ByteArrayInputStream(contents);
        }

        @Override
        public int read() {
            return buffer.read();
        }

        @Override
        public int read(@NonNull byte[] b, int off, int len) {
            return buffer.read(b, off, len);
        }

        @Override
        public boolean isFinished() {
            return buffer.available() == 0;
        }

        @Override
        public boolean isReady() {
            return true;
        }

        @Override
        public void setReadListener(ReadListener listener) {
            throw new UnsupportedOperationException("setReadListener is not supported");
        }
    }
} 