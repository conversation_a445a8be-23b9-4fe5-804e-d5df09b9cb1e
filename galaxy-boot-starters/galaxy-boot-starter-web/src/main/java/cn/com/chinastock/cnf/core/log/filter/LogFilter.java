package cn.com.chinastock.cnf.core.log.filter;

import cn.com.chinastock.cnf.core.log.GalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import cn.com.chinastock.cnf.core.log.config.LogProperties;
import cn.com.chinastock.cnf.core.log.context.ITraceContext;
import cn.com.chinastock.cnf.core.log.context.LogContext;
import cn.com.chinastock.cnf.core.log.logger.FrameworkLogger;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.springframework.core.Ordered;
import org.springframework.lang.NonNull;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.util.ContentCachingResponseWrapper;

import java.io.IOException;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

/**
 * Log Context 过滤器
 *
 * <p>过滤器，主要功能如下：</p>
 * <ul>
 *     <li>处理请求中的信息，提取 HTTP method、HTTP request URI、 traceId、spanId 和 parentSpanId</li>
 *     <li>记录请求日志（可通过配置控制）</li>
 *     <li>记录响应日志（可通过配置控制）</li>
 *     <li>记录性能日志（可通过配置控制）</li>
 * </ul>
 */
public class LogFilter extends OncePerRequestFilter implements Ordered {
    private final ITraceContext traceContext;
    private final LogProperties logProperties;
    private final FrameworkLogger frameworkLogger;

    public LogFilter(ITraceContext traceContext, LogProperties logProperties) {
        this.traceContext = traceContext;
        this.logProperties = logProperties;
        this.frameworkLogger = new FrameworkLogger(logProperties);
    }

    @Override
    protected void doFilterInternal(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response, @NonNull FilterChain filterChain) throws ServletException, IOException {
        loadLogContext(request);
        CacheableRequestWrapper requestWrapper = new CacheableRequestWrapper(request);
        ContentCachingResponseWrapper responseWrapper = new ContentCachingResponseWrapper(response);
        writeRequestLogAndStartWatch(requestWrapper);

        try {
            filterChain.doFilter(requestWrapper, responseWrapper);
        } finally {
            writeResponseLogAndStopWatch(responseWrapper);
        }
    }

    private void loadLogContext(HttpServletRequest request) {
        LogContext.current().loadContext(logProperties, traceContext, request.getMethod(), request.getRequestURI(), getRequestHeaders(request));
    }

    private void writeRequestLogAndStartWatch(CacheableRequestWrapper requestWrapper) {
        try {
            frameworkLogger.startPerformanceWatch();
            frameworkLogger.logRequest(requestWrapper);
        } catch (Exception e) {
            GalaxyLogger.error(LogCategory.EXCEPTION_LOG, "writeRequestLogAndStartWatch: exception occurred", e);
        }
    }

    private void writeResponseLogAndStopWatch(ContentCachingResponseWrapper responseWrapper) {
        try {
            frameworkLogger.logResponse(responseWrapper);
            responseWrapper.copyBodyToResponse();
        } catch (Exception e) {
            GalaxyLogger.error(LogCategory.EXCEPTION_LOG, "writeResponseLogAndStopWatch: response logging failed", e);
        } finally {
            safeStopPerformanceWatch();
            LogContext.clear();
        }
    }



    /**
     * 安全地停止性能监控
     *
     * <p>即使在异常情况下也能确保性能监控正确停止，避免影响后续请求。
     * 如果停止失败，会尝试重置性能监控状态。</p>
     */
    private void safeStopPerformanceWatch() {
        try {
            frameworkLogger.stopPerformanceWatch();
        } catch (Exception e) {
            GalaxyLogger.error(LogCategory.EXCEPTION_LOG, "safeStopPerformanceWatch: failed to stop performance watch", e);
            // 如果停止失败，尝试重置状态以确保下次能正常使用
            try {
                frameworkLogger.resetPerformanceWatch();
                GalaxyLogger.warn(LogCategory.FRAMEWORK_LOG, "Performance watch state has been reset due to stop failure");
            } catch (Exception resetException) {
                GalaxyLogger.error(LogCategory.EXCEPTION_LOG, "safeStopPerformanceWatch: failed to reset performance watch", resetException);
            }
        }
    }

    private Map<String, String> getRequestHeaders(HttpServletRequest httpRequest) {
        Map<String, String> headers = new HashMap<>();
        // Get the header names as an Enumeration
        Enumeration<String> headerNames = httpRequest.getHeaderNames();

        // Iterate over the Enumeration using a while loop
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            headers.put(headerName, httpRequest.getHeader(headerName));
        }

        return headers;
    }

    @Override
    public int getOrder() {
        // 确保在 OpenTelemetry tracing filter 之后执行
        return Ordered.HIGHEST_PRECEDENCE + 2000;
    }
}