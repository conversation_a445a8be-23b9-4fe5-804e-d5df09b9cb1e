package cn.com.chinastock.cnf.core.log.logger;

import cn.com.chinastock.cnf.core.log.config.LogProperties;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 框架日志记录器
 *
 * <p>用于记录请求日志、响应日志和性能日志</p>
 *
 * <p>该类中包含以下公共方法：</p>
 *
 * <ul>
 *     <li>{@link #logRequest(HttpServletRequest)}：记录请求日志。</li>
 *     <li>{@link #logResponse(HttpServletResponse)}：记录响应日志。</li>
 *     <li>{@link #startPerformanceWatch()}：启动性能监控。</li>
 *     <li>{@link #stopPerformanceWatch()}：停止性能监控并记录耗时。</li>
 * </ul>
 *
 * <AUTHOR>
 */
public class FrameworkLogger {
    private final LogProperties logProperties;
    private final RequestLogger requestLogger;
    private final ResponseLogger responseLogger;
    private final PerformanceLogger performanceLogger;

    /**
     * 构造函数
     *
     * @param logProperties 日志配置属性
     */
    public FrameworkLogger(LogProperties logProperties) {
        this.logProperties = logProperties;
        this.requestLogger = new RequestLogger(logProperties);
        this.responseLogger = new ResponseLogger(logProperties);
        this.performanceLogger = new PerformanceLogger();
    }

    /**
     * 记录HTTP请求日志
     *
     * @param request HTTP请求对象
     */
    public void logRequest(HttpServletRequest request) {
        if (logProperties.isReqResLogEnabledButNotControllerLog()) {
            requestLogger.log(request);
        }
    }

    /**
     * 记录HTTP响应日志
     *
     * @param response HTTP响应对象
     */
    public void logResponse(HttpServletResponse response) {
        if (logProperties.isReqResLogEnabledButNotControllerLog()) {
            responseLogger.log(response);
        }
    }

    /**
     * 启动性能监控
     */
    public void startPerformanceWatch() {
        if (logProperties.isPerformanceLogEnabled()) {
            performanceLogger.start();
        }
    }

    /**
     * 停止性能监控并记录耗时
     */
    public void stopPerformanceWatch() {
        if (logProperties.isPerformanceLogEnabled()) {
            performanceLogger.stop();
        }
    }

    /**
     * 重置性能监控状态
     *
     * <p>在异常情况下重置性能监控状态，确保下次能正常使用。
     * 此方法主要用于异常处理场景，避免StopWatch状态异常影响后续请求。</p>
     */
    public void resetPerformanceWatch() {
        if (logProperties.isPerformanceLogEnabled()) {
            performanceLogger.resetStopWatch();
        }
    }

    /**
     * 记录请求日志，并对带注解字段进行掩码处理。
     *
     * @param requestObject 需要记录的请求对象
     */
    public void logRequestWithFieldsMasked(Object requestObject) {
        if (logProperties.isControllerLogEnabled()) {
            requestLogger.logWithFieldsMasked(requestObject);
        }
    }

    /**
     * 记录响应日志，并对敏感字段进行脱敏处理。
     *
     * @param contentType    响应内容类型
     * @param headers        响应头信息
     * @param statusCode     HTTP状态码
     * @param responseObject 响应对象
     */
    public void logResponseWithFieldsMasked(MediaType contentType, HttpHeaders headers, int statusCode, Object responseObject) {
        if (logProperties.isControllerLogEnabled()) {
            responseLogger.logWithFieldsMasked(contentType, headers, statusCode, responseObject);
        }
    }
}
