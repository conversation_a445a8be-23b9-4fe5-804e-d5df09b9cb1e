package cn.com.chinastock.cnf.core.log;

import cn.com.chinastock.cnf.core.log.context.LogContext;

import java.util.Objects;

/**
 * GalaxyBoot Web日志组件遵循 Galaxy日志规范，提供了统一的、规范化的日志功能。
 *
 * <p>目前日志需求主要是以下几个方面</p>
 * <ul>
 *     <li>日志规范的统一：目前各个系统打印的日志格式多种多样，从日志收集的角度很难以统一的方式解析，也因此无法将关键信息进行结构化而支持更高效的检索，存在一定的查询性能问题。</li>
 *     <li>日志支持全链路追踪：目前无法通过日志查询到一个请求的上下游相关日志，很难定位问题。</li>
 *     <li>日志保存时限要求：根据运营标准中日志管理域的要求，对于不同的日志类别有不同的保存时限要求，需要在日志收集和转储过程中进行控制。</li>
 * </ul>
 *
 * <p>GalaxyBoot 日志组件使用示例：</p>
 * <pre>
 *     {@code
 *      // INFO级别， 日志类别为业务日志
 *      galaxyWebLogger.info(LogCategory.BUSINESS_LOG, "生成8位ID: {}", id);
 *
 *      // ERROR级别，日志类别为业务日志
 *      galaxyWebLogger.error(LogCategory.BUSINESS_LOG, "请求ID长度不支持, length={}", idDTO.getId().length());
 *
 *      // ERROR级别异常日志，打印异常堆栈，日志类别为异常日志
 *      galaxyWebLogger.error(LogCategory.EXCEPTION_LOG, "生成ID时发生异常", e);
 *
 *      // DEBUG级别，日志类别为业务日志
 *      galaxyWebLogger.debug(LogCategory.BUSINESS_LOG, "请求ID: {}", idDTO.getId());
 *
 *      // WARN级别，日志类别为业务日志
 *      galaxyWebLogger.warn(LogCategory.BUSINESS_LOG, "请求ID长度不支持, length={}", idDTO.getId().length());
 *
 *      // INFO级别，日志类别为默认日志类别（可通过配置指定，默认为APP_LOG）
 *      galaxyWebLogger.info("生成8位ID: {}", id);
 *
 *      // ERROR级别，日志类别为默认日志类别（可通过配置指定，默认为APP_LOG）
 *      galaxyWebLogger.error("请求ID长度不支持, length={}", idDTO.getId().length());
 *
 *      // ERROR级别异常日志，打印异常堆栈，日志类别为默认日志类别（可通过配置指定，默认为APP_LOG）
 *      galaxyWebLogger.error("生成ID时发生异常", e);
 *
 *      // DEBUG级别，日志类别为默认日志类别（可通过配置指定，默认为APP_LOG）
 *      galaxyWebLogger.debug("请求ID: {}", idDTO.getId());
 *
 *      // WARN级别，日志类别为默认日志类别（可通过配置指定，默认为APP_LOG）
 *      galaxyWebLogger.warn("请求ID长度不支持, length={}", idDTO.getId().length());
 *     }
 * </pre>
 *
 * <p>该类中包含以下方法：</p>
 * <ul>
 *     <li>{@link #info(String, Object...)}：记录信息级别的日志。</li>
 *     <li>{@link #error(String, Throwable)}：记录错误日志，包含异常对象。</li>
 *     <li>{@link #error(String, Object...)}：记录错误日志，支持格式化参数。</li>
 *     <li>{@link #warn(String, Object...)}：记录警告日志。</li>
 *     <li>{@link #debug(String, Object...)}：打印调试信息。</li>
 *     <li>{@link #info(LogCategory, String, Object...)}：记录指定类别的INFO级别日志。</li>
 *     <li>{@link #error(LogCategory, String, Throwable)}：记录指定类别的ERROR级别日志，包含异常对象。</li>
 *     <li>{@link #error(LogCategory, String, Object...)}：记录指定类别的ERROR级别日志，支持格式化参数。</li>
 *     <li>{@link #warn(LogCategory, String, Object...)}：记录指定类别的WARN级别日志。</li>
 *     <li>{@link #debug(LogCategory, String, Object...)}：记录指定类别的DEBUG级别日志。</li>
 * </ul>
 *
 * <AUTHOR>
 * @see LogCategory
 */
public class GalaxyWebLogger implements IGalaxyLogger {

    private final String logName;

    public GalaxyWebLogger() {
        this.logName = null;
    }

    public GalaxyWebLogger(String logName) {
        this.logName = logName;
    }

    /**
     * 记录信息级别的日志。
     *
     * <pre>
     *    {@code
     *        galaxyWebLogger.info("User logged in: {}", user.getName());
     *        // 输出: User logged in: JohnDoe
     *    }
     * </pre>
     *
     * @param message 日志消息模板，支持占位符 {}
     * @param args    日志消息的参数，用于替换占位符
     */
    @Override
    public void info(String message, Object... args) {
        LogContext context = LogContext.current();
        context.setLogCategory(context.getDefaultCategory());
        context.getLogger(getCallerClass()).info(escapeAndTruncateMessage(message, args, context.getMaxLogLength()));
        context.setFrameworkLogAsDefault();
    }

    /**
     * 记录错误日志
     *
     * <pre>
     *    {@code
     *      galaxyWebLogger.error("An error occurred", new RuntimeException("error"));
     *      // 输出异常堆栈
     *    }
     * </pre>
     *
     * @param message 错误信息
     * @param t       异常对象
     */
    @Override
    public void error(String message, Throwable t) {
        LogContext context = LogContext.current();
        context.setLogCategory(context.getDefaultCategory());
        context.getLogger(getCallerClass()).error(escapeAndTruncateMessage(message, null, context.getMaxLogLength()), t);
        context.setFrameworkLogAsDefault();
    }

    /**
     * 记录错误日志
     *
     * <pre>
     *    {@code
     *        galaxyWebLogger.error("An error occurred: %s", "Invalid input");
     *        // 输出: "An error occurred: Invalid input"
     *    }
     * </pre>
     *
     * @param message 错误信息模板，支持格式化
     * @param args    格式化参数
     */
    @Override
    public void error(String message, Object... args) {
        LogContext context = LogContext.current();
        context.setLogCategory(context.getDefaultCategory());
        context.getLogger(getCallerClass()).error(escapeAndTruncateMessage(message, args, context.getMaxLogLength()));
        context.setFrameworkLogAsDefault();
    }

    /**
     * 记录警告日志
     *
     * <pre>
     *    {@code
     *        galaxyWebLogger.warn("User login failed: {}", username);
     *        // 输出: User login failed: admin
     *    }
     * </pre>
     *
     * @param message 日志消息模板，支持占位符 {}
     * @param args    日志消息的参数
     */
    @Override
    public void warn(String message, Object... args) {
        LogContext context = LogContext.current();
        context.setLogCategory(context.getDefaultCategory());
        context.getLogger(getCallerClass()).warn(escapeAndTruncateMessage(message, args, context.getMaxLogLength()));
        context.setFrameworkLogAsDefault();
    }

    /**
     * 打印调试信息
     *
     * <pre>
     *    {@code
     *        galaxyWebLogger.debug("User logged in: %s", user.getName());
     *        // 输出: "User logged in: JohnDoe"
     *    }
     * </pre>
     *
     * @param message 调试信息模板，支持格式化字符串
     * @param args    格式化字符串的参数
     */
    @Override
    public void debug(String message, Object... args) {
        LogContext context = LogContext.current();
        context.setLogCategory(context.getDefaultCategory());
        context.getLogger(getCallerClass()).debug(escapeAndTruncateMessage(message, args, context.getMaxLogLength()));
        context.setFrameworkLogAsDefault();
    }

    /**
     * 记录INFO级别日志
     * <pre>
     *     {@code
     *     galaxyWebLogger.info(LogCategory.USER_ACTION, "User {} logged in at {}", userId, timestamp);
     *     }
     * </pre>
     *
     * @param logCategory 日志类别
     * @param message     日志消息
     * @param args        参数
     */
    @Override
    public void info(LogCategory logCategory, String message, Object... args) {
        LogContext context = LogContext.current();
        context.setLogCategory(logCategory);
        context.getLogger(getCallerClass()).info(escapeAndTruncateMessage(message, args, context.getMaxLogLength()));
        context.setFrameworkLogAsDefault();
    }

    /**
     * 记录ERROR级别日志
     * <pre>
     *     {@code
     *     galaxyWebLogger.error(LogCategory.EXCEPTION_LOG, "An error occurred", new RuntimeException("error"));
     *     // 输出异常堆栈
     *     }
     * </pre>
     *
     * @param logCategory 日志类别
     * @param message     日志消息
     * @param t           异常
     */
    @Override
    public void error(LogCategory logCategory, String message, Throwable t) {
        LogContext context = LogContext.current();
        context.setLogCategory(logCategory);
        context.getLogger(getCallerClass()).error(escapeAndTruncateMessage(message, null, context.getMaxLogLength()), t);
        context.setFrameworkLogAsDefault();
    }

    /**
     * 记录ERROR级别日志
     * <pre>
     *     {@code
     *     galaxyWebLogger.error(LogCategory.BUSINESS_LOG, "An error occurred, userId={}", userId);
     *     }
     * </pre>
     *
     * @param logCategory 日志类别
     * @param message     日志消息
     * @param args        参数
     */
    @Override
    public void error(LogCategory logCategory, String message, Object... args) {
        LogContext context = LogContext.current();
        context.setLogCategory(logCategory);
        context.getLogger(getCallerClass()).error(escapeAndTruncateMessage(message, args, context.getMaxLogLength()));
        context.setFrameworkLogAsDefault();
    }

    /**
     * 记录WARN级别日志
     * <pre>
     *     {@code
     *     galaxyWebLogger.warn(LogCategory.BUSINESS_LOG, "An warning occurred, userId={}", userId);
     *     }
     * </pre>
     *
     * @param logCategory 日志类别
     * @param message     日志消息
     * @param args        参数
     */
    @Override
    public void warn(LogCategory logCategory, String message, Object... args) {
        LogContext context = LogContext.current();
        context.setLogCategory(logCategory);
        context.getLogger(getCallerClass()).warn(escapeAndTruncateMessage(message, args, context.getMaxLogLength()));
        context.setFrameworkLogAsDefault();
    }

    /**
     * 记录DEBUG级别日志
     * <pre>
     *     {@code
     *     galaxyWebLogger.debug(LogCategory.BUSINESS_LOG, "An debug message, userId={}", userId);
     *     }
     * </pre>
     *
     * @param logCategory 日志类别
     * @param message     日志消息
     * @param args        参数
     */
    @Override
    public void debug(LogCategory logCategory, String message, Object... args) {
        LogContext context = LogContext.current();
        context.setLogCategory(logCategory);
        context.getLogger(getCallerClass()).debug(escapeAndTruncateMessage(message, args, context.getMaxLogLength()));
        context.setFrameworkLogAsDefault();
    }

    /**
     * 获取调用者的类名
     *
     * @return 调用者的类名
     */
    private String getCallerClass() {
        if (Objects.nonNull(logName)) {
            return logName;
        }
        StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
        // index 0 是 getStackTrace
        // index 1 是 getCallerClassName
        // index 2 是 log 方法
        // index 3 是调用者
        return stackTrace[3].getClassName();
    }
}