package cn.com.chinastock.cnf.core.log.config;

import cn.com.chinastock.cnf.core.log.GalaxyLogger;
import cn.com.chinastock.cnf.core.log.GalaxyWebLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import cn.com.chinastock.cnf.core.log.aspect.ControllerLogAspect;
import cn.com.chinastock.cnf.core.log.context.ITraceContext;
import cn.com.chinastock.cnf.core.log.filter.LogFilter;
import org.apache.logging.log4j.core.async.AsyncLoggerContextSelector;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.core.Ordered;
import org.springframework.core.io.ClassPathResource;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.Properties;

@Configuration
@EnableConfigurationProperties(LogProperties.class)
@EnableAspectJAutoProxy
public class LoggingAutoConfiguration {

    static {
        if (System.getProperty("logging.config") == null) {
            System.setProperty("logging.config", "classpath:log4j2.xml");
        }
    }

    public LoggingAutoConfiguration() {
        GalaxyLogger.setLogger(new GalaxyWebLogger());
        GalaxyLogger.info(LogCategory.FRAMEWORK_LOG, "GalaxyBoot: Initializing LoggingAutoConfiguration");
    }

    @Bean
    public LogFilter logFilter(ITraceContext traceContext, LogProperties logProperties) {
        return new LogFilter(traceContext, logProperties);
    }

    @Bean
    public FilterRegistrationBean<LogFilter> logFilterRegistration(LogFilter logFilter) {
        GalaxyLogger.info(LogCategory.FRAMEWORK_LOG, "GalaxyBoot: Initializing LogFilter");
        FilterRegistrationBean<LogFilter> registration = new FilterRegistrationBean<>();
        registration.setFilter(logFilter);
        registration.addUrlPatterns("/*");
        registration.setName("logFilter");
        // 设置较低的优先级，确保在 OpenTelemetry tracing filter 之后执行
        registration.setOrder(Ordered.HIGHEST_PRECEDENCE + 2000);
        return registration;
    }

    @Bean
    @ConditionalOnProperty(name = PropertyConstants.GALAXY_LOG_REQUEST_RESPONSE_MASK_FIELD, havingValue = "true")
    public ControllerLogAspect controllerLogAspect(LogProperties logProperties) {
        return new ControllerLogAspect(logProperties);
    }

    @PostConstruct
    public void initAndLogFrameworkVersions() {
        // 打印日志异步配置
        boolean selected = AsyncLoggerContextSelector.isSelected();
        GalaxyLogger.info(LogCategory.FRAMEWORK_LOG, String.format("GalaxyBoot: AsyncLoggerContextSelector=%b", selected));
        // 打印框架版本
        String version = readGalaxyBootVersion("version");
        GalaxyLogger.info(LogCategory.FRAMEWORK_LOG, "GalaxyBoot: FrameworkVersion=" + version);
        // 打印SpringFramework版本
        String springFrameWorkVersion = org.springframework.core.SpringVersion.getVersion();
        String springBootVersion = readGalaxyBootVersion("springBootVersion");
        GalaxyLogger.info(LogCategory.FRAMEWORK_LOG, "GalaxyBoot: SpringBootVersion={}, SpringFrameworkVersion={}", springBootVersion, springFrameWorkVersion);
    }

    private String readGalaxyBootVersion(String versionName) {
        try {
            Properties props = new Properties();
            ClassPathResource resource = new ClassPathResource(PropertyConstants.GALAXY_BOOT_PROPERTIES);
            props.load(resource.getInputStream());
            return props.getProperty(versionName, "unknown");
        } catch (IOException e) {
            return "unknown";
        }
    }
}