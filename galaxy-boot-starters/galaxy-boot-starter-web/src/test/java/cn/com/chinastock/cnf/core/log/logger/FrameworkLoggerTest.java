package cn.com.chinastock.cnf.core.log.logger;

import cn.com.chinastock.cnf.core.log.GalaxyLogger;
import cn.com.chinastock.cnf.core.log.GalaxyWebLogger;
import cn.com.chinastock.cnf.core.log.config.LogProperties;
import cn.com.chinastock.cnf.core.log.context.LogContext;
import cn.com.chinastock.cnf.core.log.context.W3CTraceContext;
import cn.com.chinastock.cnf.core.log.filter.CacheableRequestWrapper;
import cn.com.chinastock.cnf.core.log.helper.TestLogAppender;
import org.apache.logging.log4j.Level;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.core.Appender;
import org.apache.logging.log4j.core.Filter;
import org.apache.logging.log4j.core.Logger;
import org.apache.logging.log4j.core.LoggerContext;
import org.apache.logging.log4j.core.filter.ThresholdFilter;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartHttpServletRequest;
import org.springframework.mock.web.MockPart;
import org.springframework.web.util.ContentCachingResponseWrapper;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class FrameworkLoggerTest {

    @Mock
    private CacheableRequestWrapper request;

    @Mock
    private ContentCachingResponseWrapper response;

    private LogProperties logProperties;
    private FrameworkLogger frameworkLogger;
    private TestLogAppender appender;


    @BeforeEach
    void setUp() {
        GalaxyLogger.setLogger(new GalaxyWebLogger());

        // 当前测试文件主要测试日志的输出功能，设置日志输出为同步输出
        System.setProperty("log4j2.contextSelector",
                "org.apache.logging.log4j.core.selector.ClassLoaderContextSelector");

        // 设置Log4j2测试appender
        LoggerContext context = (LoggerContext) LogManager.getContext(false);

        // 为所有相关的 Logger 添加 TestLogAppender
        appender = new TestLogAppender(ThresholdFilter.createFilter(Level.ALL, Filter.Result.ACCEPT, Filter.Result.DENY));
        appender.start();

        // 配置 GalaxyLogger 使用的 logger（这是关键，因为所有日志都通过 GalaxyLogger）
        String galaxyLoggerName = "cn.com.chinastock.cnf.core.log.GalaxyLogger";
        Logger galaxyLogger = context.getLogger(galaxyLoggerName);
        clearAndAddAppender(galaxyLogger, appender);

        // 配置根 logger 以捕获所有日志
        Logger rootLogger = context.getRootLogger();
        clearAndAddAppender(rootLogger, appender);

        // 配置测试类的 logger 以捕获测试中的日志调用
        Logger testLogger = (Logger) context.getLogger(FrameworkLoggerTest.class);
        clearAndAddAppender(testLogger, appender);

        // 初始化LogContext
        LogContext logContext = LogContext.current();
        logProperties = new LogProperties();

        W3CTraceContext traceContext = new W3CTraceContext();
        logContext.loadContext(logProperties, traceContext, "POST", "/api/test", new HashMap<>());

        this.frameworkLogger = new FrameworkLogger(logProperties);
    }

    @AfterEach
    void tearDown() {
        // 清理appender中的事件
        if (appender != null) {
            appender.clear();
        }
        // 清理LogContext
        LogContext.clear();
    }

    /**
     * 清除现有的 Appender 并添加测试 Appender
     *
     * @param logger       需要配置的 Logger
     * @param testAppender 测试用的 Appender
     */
    private void clearAndAddAppender(Logger logger, Appender testAppender) {
        // 移除已有的Appender
        for (Appender appender : logger.getAppenders().values()) {
            logger.removeAppender(appender);
        }

        // 添加测试Appender
        logger.addAppender(testAppender);
        logger.setLevel(Level.ALL);
        logger.setAdditive(false);
    }

    @Test
    void shouldLogRequestWithHeadersAndBody() {
        // 配置启用请求日志
        logProperties.setRequestResponseEnabled(true);
        logProperties.setRequestHeadersEnabled(true);

        // Mock请求头
        when(request.getQueryString()).thenReturn("param1=value1&param2=value2");
        when(request.getHeaderNames()).thenReturn(Collections.enumeration(
                Collections.singletonList("Content-Type")));
        when(request.getHeader("Content-Type")).thenReturn(MediaType.APPLICATION_JSON_VALUE);

        // Mock请求体
        String requestBody = "{\"key\":\"value\"}";
        when(request.getCachedBody()).thenReturn(requestBody.getBytes());
        when(request.isCached()).thenReturn(true);

        // 执行日志记录
        frameworkLogger.logRequest(request);

        // 验证日志内容
        assertThat(appender.getEvents()).hasSize(1);
        assertThat(appender.getEvents().get(0).getMessage().getFormattedMessage())
                .contains("query_string=param1=value1&param2=value2")
                .contains("headers={\"Content-Type\":\"application/json\"}")
                .contains("body={\"key\":\"value\"}");
    }

    @Test
    void shouldNotLogRequestWhenDisabled() {
        // 配置禁用请求日志
        logProperties.setRequestResponseEnabled(false);

        frameworkLogger.logRequest(request);

        // 验证没有产生日志
        assertThat(appender.getEvents()).isEmpty();
    }

    @Test
    void shouldNotLogRequestHeadersWhenDisabled() {
        // 配置启用请求日志，但禁用请求头
        logProperties.setRequestResponseEnabled(true);
        logProperties.setRequestHeadersEnabled(false);

        // Mock请求头
        when(request.getQueryString()).thenReturn("param1=value1&param2=value2");

        // Mock请求体
        String requestBody = "{\"key\":\"value\"}";
        when(request.getCachedBody()).thenReturn(requestBody.getBytes());
        when(request.isCached()).thenReturn(true);

        // 执行日志记录
        frameworkLogger.logRequest(request);

        // 验证日志内容
        assertThat(appender.getEvents()).hasSize(1);
        assertThat(appender.getEvents().get(0).getMessage().getFormattedMessage())
                .contains("query_string=param1=value1&param2=value2")
                .contains("headers=null")
                .contains("body={\"key\":\"value\"}");
    }

    @Test
    void shouldLogResponseWithBody() {
        // 配置启用响应日志
        logProperties.setRequestResponseEnabled(true);

        // Mock the original response first
        when(response.getStatus()).thenReturn(200);
        when(response.getContentType()).thenReturn(MediaType.APPLICATION_JSON_VALUE);

        // Write response body
        String responseBody = "{\"result\":\"success\"}";
        when(response.getContentAsByteArray()).thenReturn(responseBody.getBytes());

        // 执行日志记录
        frameworkLogger.logResponse(response);

        // 验证日志内容
        assertThat(appender.getEvents()).hasSize(1);
        assertThat(appender.getEvents().get(0).getMessage().getFormattedMessage())
                .contains("http_status_code=200")
                .contains("body={\"result\":\"success\"}");
    }

    @Test
    void shouldNotLogResponseWhenDisabled() {
        // 配置禁用响应日志
        logProperties.setRequestResponseEnabled(false);

        // 执行日志记录
        frameworkLogger.logResponse(response);

        // 验证没有产生日志
        assertThat(appender.getEvents()).isEmpty();
    }

    @Test
    void shouldLogPerformanceMetrics() throws InterruptedException {
        // 配置启用性能日志
        logProperties.setPerformanceLogEnabled(true);

        // 执行性能监控
        frameworkLogger.startPerformanceWatch();
        Thread.sleep(1); // 模拟处理时间
        frameworkLogger.stopPerformanceWatch();

        // 验证日志内容
        assertThat(appender.getEvents()).hasSize(1);
        String message = appender.getEvents().get(0).getMessage().getFormattedMessage();

        assertThat(message)
                .matches("cost=\\d+ unit=ms")
                .contains("unit=ms");

        // 验证记录的处理时间是否合理（大于等于休眠时间）
        String costStr = message.substring(message.indexOf("=") + 1, message.indexOf(" unit"));
        long cost = Long.parseLong(costStr);
        assertThat(cost).isGreaterThanOrEqualTo(1);
    }

    @Test
    void shouldNotLogPerformanceMetricsWhenDisabled() throws InterruptedException {
        // 配置禁用性能日志
        logProperties.setPerformanceLogEnabled(false);

        // 执行性能监控
        frameworkLogger.startPerformanceWatch();
        Thread.sleep(1); // 模拟处理时间
        frameworkLogger.stopPerformanceWatch();

        // 验证没有产生日志
        assertThat(appender.getEvents()).isEmpty();
    }

    @Test
    void shouldLogRequestWithFlattenedJsonBodyAndUrlEncodedEquals() {
        // Given
        logProperties.setRequestResponseEnabled(true);
        logProperties.setRequestHeadersEnabled(true);

        when(request.getQueryString()).thenReturn("param1=value1&param2=value2");
        when(request.getHeaderNames()).thenReturn(Collections.enumeration(
                Collections.singletonList("Content-Type")));
        when(request.getHeader("Content-Type")).thenReturn(MediaType.APPLICATION_JSON_VALUE);

        String requestBody = "{\n\"key\":\"value\"\n}";
        when(request.getCachedBody()).thenReturn(requestBody.getBytes());
        when(request.isCached()).thenReturn(true);

        // When
        frameworkLogger.logRequest(request);

        // Then
        assertThat(appender.getEvents()).hasSize(1);
        assertThat(appender.getEvents().get(0).getMessage().getFormattedMessage())
                .contains("query_string=param1=value1&param2=value2")
                .contains("headers={\"Content-Type\":\"application/json\"}")
                .contains("body={\"key\":\"value\"}");
    }

    @Test
    void shouldLogResponseWithFlattenedJsonBodyAndUrlEncodedEquals() {
        // Given
        logProperties.setRequestResponseEnabled(true);

        when(response.getStatus()).thenReturn(200);
        when(response.getContentType()).thenReturn(MediaType.APPLICATION_JSON_VALUE);

        String responseBody = "{\n\"result\":\"success\"\n}";
        when(response.getContentAsByteArray()).thenReturn(responseBody.getBytes());

        // When
        frameworkLogger.logResponse(response);

        // When
        assertThat(appender.getEvents()).hasSize(1);
        assertThat(appender.getEvents().get(0).getMessage().getFormattedMessage())
                .contains("http_status_code=200")
                .contains("body={\"result\":\"success\"}");
    }

    @Test
    void shouldLogResponseWithMetaCode() {
        // Given
        logProperties.setRequestResponseEnabled(true);
        when(response.getStatus()).thenReturn(200);
        when(response.getContentType()).thenReturn(MediaType.APPLICATION_JSON_UTF8_VALUE);

        String responseBody = "{\"meta\":{\"code\":\"XXXTCNF000\",\"message\":\"success\"},\"data\":{\"id\":\"123\"}}";
        when(response.getContentAsByteArray()).thenReturn(responseBody.getBytes());

        // When
        frameworkLogger.logResponse(response);

        // Then
        assertThat(appender.getEvents()).hasSize(1);
        assertThat(appender.getEvents().get(0).getMessage().getFormattedMessage())
                .contains("http_status_code=200")
                .contains("meta_code=XXXTCNF000")
                .contains("body=");
    }

    @Test
    void shouldLogResponseWithoutMetaCode() {
        // Given
        logProperties.setRequestResponseEnabled(true);
        when(response.getStatus()).thenReturn(200);
        when(response.getContentType()).thenReturn(MediaType.APPLICATION_JSON_VALUE);

        String responseBody = "{\"data\":{\"id\":\"123\"}}";
        when(response.getContentAsByteArray()).thenReturn(responseBody.getBytes());

        // When
        frameworkLogger.logResponse(response);

        // Then
        assertThat(appender.getEvents()).hasSize(1);
        assertThat(appender.getEvents().get(0).getMessage().getFormattedMessage())
                .contains("http_status_code=200")
                .contains("meta_code=null")
                .contains("body=");
    }

    @Test
    void shouldHandleInvalidJson() {
        // Given
        logProperties.setRequestResponseEnabled(true);
        when(response.getStatus()).thenReturn(200);
        when(response.getContentType()).thenReturn(MediaType.APPLICATION_JSON_VALUE);

        String invalidJson = "{ invalid json }";
        when(response.getContentAsByteArray()).thenReturn(invalidJson.getBytes());

        // When
        frameworkLogger.logResponse(response);

        // Then
        assertThat(appender.getEvents()).hasSize(1);
        assertThat(appender.getEvents().get(0).getMessage().getFormattedMessage())
                .contains("http_status_code=200")
                .contains("meta_code=null")
                .contains("body=");
    }

    @Test
    void shouldLogMultipartFileUploadRequest() {
        // Given
        logProperties.setRequestResponseEnabled(true);

        // 创建 MockMultipartHttpServletRequest
        MockMultipartHttpServletRequest request = new MockMultipartHttpServletRequest();

        // 添加两个模拟文件
        request.addPart(new MockPart("document", "test.pdf", new byte[1024 * 1024]));
        request.addPart(new MockPart("image", "photo.jpg", new byte[2048]));

        // When
        frameworkLogger.logRequest(request);

        // Then
        assertThat(appender.getEvents()).hasSize(1);
        assertThat(appender.getEvents().get(0).getMessage().getFormattedMessage())
                .contains("body=[{\"file_name\":\"test.pdf\",\"file_size\":\"1.0MB\"},{\"file_name\":\"photo.jpg\",\"file_size\":\"2.0KB\"}]");
    }

    @Test
    void shouldHandleEmptyMultipartRequest() {
        // Given
        logProperties.setRequestResponseEnabled(true);

        // 创建 MockMultipartHttpServletRequest
        MockMultipartHttpServletRequest request = new MockMultipartHttpServletRequest();

        // When
        frameworkLogger.logRequest(request);

        // Then
        assertThat(appender.getEvents()).hasSize(1);
        assertThat(appender.getEvents().get(0).getMessage().getFormattedMessage())
                .contains("body=null");  // 没有文件时 body 应该为 null
    }

    @Test
    void shouldLogFileDownloadResponse() {
        // Given
        logProperties.setRequestResponseEnabled(true);

        when(response.getStatus()).thenReturn(200);
        when(response.getContentType()).thenReturn(MediaType.APPLICATION_OCTET_STREAM_VALUE);
        when(response.getHeader("Content-Disposition"))
                .thenReturn("attachment; filename=\"test-file.pdf\"");
        when(response.getHeader("Content-Length"))
                .thenReturn("1048576"); // 1MB

        // When
        frameworkLogger.logResponse(response);

        // Then
        assertThat(appender.getEvents()).hasSize(1);
        assertThat(appender.getEvents().get(0).getMessage().getFormattedMessage())
                .contains("http_status_code=200")
                .contains("body={\"file_name\":\"test-file.pdf\",\"file_size\":\"1.0MB\"}");
    }

    @Test
    void shouldLogFileDownloadResponseWithUTF8Filename() {
        // Given
        logProperties.setRequestResponseEnabled(true);

        when(response.getStatus()).thenReturn(200);
        when(response.getContentType()).thenReturn(MediaType.APPLICATION_PDF_VALUE);
        when(response.getHeader("Content-Disposition"))
                .thenReturn("attachment; filename*=UTF-8''%E6%B5%8B%E8%AF%95%E6%96%87%E4%BB%B6.pdf");
        when(response.getHeader("Content-Length"))
                .thenReturn("2048"); // 2KB

        // When
        frameworkLogger.logResponse(response);

        // Then
        assertThat(appender.getEvents()).hasSize(1);
        assertThat(appender.getEvents().get(0).getMessage().getFormattedMessage())
                .contains("http_status_code=200")
                .contains("body={\"file_name\":\"测试文件.pdf\",\"file_size\":\"2.0KB\"}");
    }

    @Test
    void shouldLogResponseWithHeaders() {
        // Given
        logProperties.setRequestResponseEnabled(true);
        logProperties.setResponseHeadersEnabled(true);

        when(response.getStatus()).thenReturn(200);
        when(response.getContentType()).thenReturn(MediaType.APPLICATION_JSON_VALUE);
        when(response.getHeaderNames()).thenReturn(Arrays.asList("Content-Type", "X-Custom-Header"));
        when(response.getHeader("Content-Type")).thenReturn(MediaType.APPLICATION_JSON_VALUE);
        when(response.getHeader("X-Custom-Header")).thenReturn("custom-value");

        String responseBody = "{\"result\":\"success\"}";
        when(response.getContentAsByteArray()).thenReturn(responseBody.getBytes());

        // When
        frameworkLogger.logResponse(response);

        // Then
        assertThat(appender.getEvents()).hasSize(1);
        assertThat(appender.getEvents().get(0).getMessage().getFormattedMessage())
                .contains("http_status_code=200")
                .contains("headers=")
                .contains("\"Content-Type\":\"application/json\"")
                .contains("\"X-Custom-Header\":\"custom-value\"")
                .contains("body={\"result\":\"success\"}");
    }

    @Test
    void shouldNotLogResponseHeadersWhenDisabled() {
        // Given
        logProperties.setRequestResponseEnabled(true);
        logProperties.setResponseHeadersEnabled(false);

        when(response.getStatus()).thenReturn(200);
        when(response.getContentType()).thenReturn(MediaType.APPLICATION_JSON_VALUE);

        String responseBody = "{\"result\":\"success\"}";
        when(response.getContentAsByteArray()).thenReturn(responseBody.getBytes());

        // When
        frameworkLogger.logResponse(response);

        // Then
        assertThat(appender.getEvents()).hasSize(1);
        assertThat(appender.getEvents().get(0).getMessage().getFormattedMessage())
                .contains("http_status_code=200")
                .contains("headers=null")
                .contains("body={\"result\":\"success\"}");
    }
}