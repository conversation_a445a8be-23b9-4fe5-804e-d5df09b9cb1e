package cn.com.chinastock.cnf.core.exception;

import cn.com.chinastock.cnf.core.log.config.LogProperties;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.Email;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import java.sql.SQLException;

import static org.hamcrest.Matchers.containsString;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

class ControllerAdviceTest {

    private MockMvc mockMvc;
    private ObjectMapper objectMapper;
    private static final String SYSTEM_CODE = "TST";

    @BeforeEach
    void setUp() {
        LogProperties logProperties = new LogProperties();
        logProperties.setSystemCode(SYSTEM_CODE);

        TestController controller = new TestController();
        ControllerAdvice controllerAdvice = new ControllerAdvice(logProperties);

        mockMvc = MockMvcBuilders.standaloneSetup(controller)
                .setControllerAdvice(controllerAdvice)
                .build();

        objectMapper = new ObjectMapper();
    }

    @Test
    void shouldReturn400WithValidationErrorsWhenValidationFails() throws Exception {
        TestDTO dto = new TestDTO();
        dto.setName("");  // 违反@NotBlank
        dto.setEmail("invalid-email");  // 违反@Email
        dto.setAge(10);  // 违反@Min(18)

        mockMvc.perform(post("/test/validate")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(dto)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.meta.success").value(false))
                .andExpect(jsonPath("$.meta.code").value(SYSTEM_CODE + "TCNF400"))
                .andExpect(jsonPath("$.meta.message", containsString("名称不能为空")))
                .andExpect(jsonPath("$.meta.message", containsString("邮箱格式不正确")))
                .andExpect(jsonPath("$.meta.message", containsString("年龄必须大于或等于18")));
    }

    @Test
    void shouldReturn200WhenValidationPasses() throws Exception {
        TestDTO dto = new TestDTO();
        dto.setName("Test Name");
        dto.setEmail("<EMAIL>");
        dto.setAge(20);

        mockMvc.perform(post("/test/validate")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(dto)))
                .andExpect(status().isOk());
    }

    @Test
    void shouldReturn200WithErrorMessageWhenBusinessExceptionThrown() throws Exception {
        mockMvc.perform(get("/test/business"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.meta.success").value(false))
                .andExpect(jsonPath("$.meta.code").value("BIZ001"))
                .andExpect(jsonPath("$.meta.message").value("测试业务异常"));
    }

    @Test
    void shouldReturn401WithErrorMessageWhenUnauthorizedExceptionThrown() throws Exception {
        mockMvc.perform(get("/test/unauthorized"))
                .andExpect(status().isUnauthorized())
                .andExpect(jsonPath("$.meta.success").value(false))
                .andExpect(jsonPath("$.meta.code").value("AUTH001"))
                .andExpect(jsonPath("$.meta.message").value("测试认证异常"));
    }

    @Test
    void shouldReturn403WithErrorMessageWhenForbiddenExceptionThrown() throws Exception {
        mockMvc.perform(get("/test/forbidden"))
                .andExpect(status().isForbidden())
                .andExpect(jsonPath("$.meta.success").value(false))
                .andExpect(jsonPath("$.meta.code").value("FORB001"))
                .andExpect(jsonPath("$.meta.message").value("测试禁止访问异常"));
    }

    @Test
    void shouldReturn500WithErrorMessageWhenServerErrorExceptionThrown() throws Exception {
        mockMvc.perform(get("/test/server-error"))
                .andExpect(status().isInternalServerError())
                .andExpect(jsonPath("$.meta.success").value(false))
                .andExpect(jsonPath("$.meta.code").value("SERV001"))
                .andExpect(jsonPath("$.meta.message").value("测试服务器异常"));
    }

    @Test
    void shouldReturn500WithErrorMessageWhenRuntimeExceptionThrown() throws Exception {
        mockMvc.perform(get("/test/runtime"))
                .andExpect(status().isInternalServerError())
                .andExpect(jsonPath("$.meta.success").value(false))
                .andExpect(jsonPath("$.meta.code").value(SYSTEM_CODE + "TCNF500"))
                .andExpect(jsonPath("$.meta.message").value("未知应用服务端异常"));
    }

    @Test
    void shouldReturn500WithErrorMessageWhenSQLExceptionThrown() throws Exception {
        mockMvc.perform(get("/test/sql"))
                .andExpect(status().isInternalServerError())
                .andExpect(jsonPath("$.meta.success").value(false))
                .andExpect(jsonPath("$.meta.code").value(SYSTEM_CODE + "TCNF500"))
                .andExpect(jsonPath("$.meta.message").value("未知应用服务端异常"));
    }

    @Test
    void shouldReturnDefaultMessagesWhenExceptionsHaveEmptyMessages() throws Exception {
        // Test BusinessException default message
        mockMvc.perform(get("/test/business-empty"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.meta.code").value(SYSTEM_CODE + "TCNF200"))
                .andExpect(jsonPath("$.meta.message").value("业务异常"));

        // Test UnauthorizedException default message
        mockMvc.perform(get("/test/unauthorized-empty"))
                .andExpect(status().isUnauthorized())
                .andExpect(jsonPath("$.meta.code").value(SYSTEM_CODE + "TCNF401"))
                .andExpect(jsonPath("$.meta.message").value("认证失败"));

        // Test ForbiddenException default message
        mockMvc.perform(get("/test/forbidden-empty"))
                .andExpect(status().isForbidden())
                .andExpect(jsonPath("$.meta.code").value(SYSTEM_CODE + "TCNF403"))
                .andExpect(jsonPath("$.meta.message").value("无权限访问"));

        // Test ServerErrorException default message
        mockMvc.perform(get("/test/server-error-empty"))
                .andExpect(status().isInternalServerError())
                .andExpect(jsonPath("$.meta.code").value(SYSTEM_CODE + "TCNF500"))
                .andExpect(jsonPath("$.meta.message").value("应用服务端异常"));
    }

    // 内部测试控制器
    @RestController
    static class TestController {

        @PostMapping("/test/validate")
        public TestDTO testValidation(@Validated @RequestBody TestDTO dto) {
            return dto;
        }


        @GetMapping("/test/business")
        public void throwBusinessException() {
            throw new BusinessException("BIZ001", "测试业务异常");
        }

        @GetMapping("/test/unauthorized")
        public void throwUnauthorizedException() {
            throw new UnauthorizedException("AUTH001", "测试认证异常");
        }

        @GetMapping("/test/forbidden")
        public void throwForbiddenException() {
            throw new ForbiddenException("FORB001", "测试禁止访问异常");
        }

        @GetMapping("/test/server-error")
        public void throwServerErrorException() {
            throw new ServerErrorException("SERV001", "测试服务器异常");
        }

        @GetMapping("/test/runtime")
        public void throwRuntimeException() {
            throw new RuntimeException("测试运行时异常");
        }

        @GetMapping("/test/sql")
        public void throwSQLException() throws SQLException {
            throw new SQLException("测试SQL异常");
        }

        @GetMapping("/test/business-empty")
        public void throwEmptyBusinessException() {
            throw new BusinessException("", "");
        }

        @GetMapping("/test/unauthorized-empty")
        public void throwEmptyUnauthorizedException() {
            throw new UnauthorizedException("");
        }

        @GetMapping("/test/forbidden-empty")
        public void throwEmptyForbiddenException() {
            throw new ForbiddenException("");
        }

        @GetMapping("/test/server-error-empty")
        public void throwEmptyServerErrorException() {
            throw new ServerErrorException("");
        }
    }

    // 内部测试DTO类
    static class TestDTO {

        @NotBlank(message = "名称不能为空")
        private String name;

        @Email(message = "邮箱格式不正确")
        private String email;

        @Min(value = 18, message = "年龄必须大于或等于18")
        private Integer age;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getEmail() {
            return email;
        }

        public void setEmail(String email) {
            this.email = email;
        }

        public Integer getAge() {
            return age;
        }

        public void setAge(Integer age) {
            this.age = age;
        }
    }

}
