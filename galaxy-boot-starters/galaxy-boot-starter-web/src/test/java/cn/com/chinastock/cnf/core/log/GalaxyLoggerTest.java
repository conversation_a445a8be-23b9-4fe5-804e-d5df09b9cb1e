package cn.com.chinastock.cnf.core.log;

import cn.com.chinastock.cnf.core.log.config.LogProperties;
import cn.com.chinastock.cnf.core.log.config.PropertyConstants;
import cn.com.chinastock.cnf.core.log.context.ITraceContext;
import cn.com.chinastock.cnf.core.log.context.LogContext;
import cn.com.chinastock.cnf.core.log.context.W3CTraceContext;
import cn.com.chinastock.cnf.core.log.filter.ThrowablePrettyPrintFilter;
import cn.com.chinastock.cnf.core.log.helper.TestLogAppender;
import org.apache.logging.log4j.Level;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.core.Appender;
import org.apache.logging.log4j.core.Filter;
import org.apache.logging.log4j.core.Logger;
import org.apache.logging.log4j.core.LoggerContext;
import org.apache.logging.log4j.core.filter.ThresholdFilter;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.MethodSource;

import java.util.HashMap;
import java.util.Map;
import java.util.stream.Stream;

import static cn.com.chinastock.cnf.core.log.context.TraceConstants.TRACE_PARENT;
import static org.assertj.core.api.Assertions.assertThat;

class GalaxyLoggerTest {

    private TestLogAppender appender;

    private Logger logger;

    private ITraceContext traceContext;

    @BeforeEach
    void setUp() {
        GalaxyLogger.setLogger(new GalaxyWebLogger());

        // 当前测试文件主要测试日志的输出功能，设置日志输出为同步输出
        System.setProperty("log4j2.contextSelector",
                "org.apache.logging.log4j.core.selector.ClassLoaderContextSelector");

        // 设置Log4j2测试appender
        LoggerContext context = (LoggerContext) LogManager.getContext(false);
        logger = (Logger) context.getLogger(GalaxyLoggerTest.class);

        // 创建测试Appender
        appender = new TestLogAppender(ThresholdFilter.createFilter(Level.ALL, Filter.Result.ACCEPT, Filter.Result.DENY));
        appender.start();

        // 配置根 logger 以捕获所有日志 - 这是关键，因为SLF4J会路由到Log4j2的根logger
        Logger rootLogger = context.getRootLogger();
        clearAndAddAppender(rootLogger, appender);

        // 配置测试类的 logger 以捕获测试中的日志调用
        Logger testLogger = (Logger) context.getLogger(GalaxyLoggerTest.class);
        clearAndAddAppender(testLogger, appender);

        // 配置GalaxyLogger类的logger，因为getCallerClass()会返回GalaxyLogger
        Logger galaxyLogger = (Logger) context.getLogger("cn.com.chinastock.cnf.core.log.GalaxyLogger");
        clearAndAddAppender(galaxyLogger, appender);

        // 初始化LogContext
        LogContext logContext = LogContext.current();
        LogProperties properties = new LogProperties();
        properties.setDefaultCategory(LogCategory.BUSINESS_LOG);

        traceContext = new W3CTraceContext();
        traceContext.extractTraceContext(new HashMap<String, String>() {
            {
                put(TRACE_PARENT, "00_43d9131b5adc42569e82b4a0ed63106a_85c9b31ab6b24e8b_01");
            }
        });
        logContext.loadContext(properties, traceContext, "POST", "/api/test", new HashMap<>());
    }

    @AfterEach
    void tearDown() {
        // 清理appender中的事件
        if (appender != null) {
            appender.clear();
        }
        // 清理LogContext
        LogContext.clear();
    }

    private void clearAndAddAppender(Logger logger, TestLogAppender appender) {
        // 移除已有的Appender
        for (Appender existingAppender : logger.getAppenders().values()) {
            logger.removeAppender(existingAppender);
        }

        // 添加测试Appender
        logger.addAppender(appender);
        logger.setLevel(Level.ALL);
        logger.setAdditive(false);
    }

    @FunctionalInterface
    private interface LogMethodWithCategory {
        void log(LogCategory category, String message, Object... args);
    }

    @FunctionalInterface
    private interface LogMethod {
        void log(String message, Object... args);
    }

    private static Stream<LogMethod> logMethodProvider() {
        return Stream.of(
                GalaxyLogger::debug,
                GalaxyLogger::info,
                GalaxyLogger::warn,
                GalaxyLogger::error
        );
    }

    private static Stream<Arguments> logMethodMultipleCategoryProvider() {
        return Stream.of(LogCategory.values())
                .flatMap(category -> Stream.of(
                        Arguments.of((LogMethodWithCategory) GalaxyLogger::debug, category),
                        Arguments.of((LogMethodWithCategory) GalaxyLogger::info, category),
                        Arguments.of((LogMethodWithCategory) GalaxyLogger::warn, category),
                        Arguments.of((LogMethodWithCategory) GalaxyLogger::error, category)
                ));
    }

    @ParameterizedTest
    @MethodSource("logMethodMultipleCategoryProvider")
    void shouldLogAllFieldsCorrect(LogMethodWithCategory logMethod, LogCategory category) {
        // Given
        String message = "Test message";
        // When
        logMethod.log(category, "Log: {}", message);
        // Then
        assertThat(appender.getEvents()).hasSize(1);
        Map<String, String> contextMap = appender.getEvents().get(0).getContextData().toMap();
        assertThat(contextMap)
                .containsEntry("log_category", category.name())
                .containsEntry("request_method", "POST")
                .containsEntry("request_uri", "/api/test")
                .containsEntry("traceId", traceContext.getTraceId())
                .containsEntry("parentSpanId", traceContext.getParentSpanId())
                .containsEntry("spanId", traceContext.getSpanId());
        // 验证traceId的格式 (32位十六进制字符)
        assertThat(contextMap.get("traceId"))
                .matches("[0-9a-f]{32}");
        // 验证spanId的格式 (16位十六进制字符)
        assertThat(contextMap.get("spanId"))
                .matches("[0-9a-f]{16}");
        // 验证Message
        assertThat(appender.getEvents().get(0).getMessage().getFormattedMessage())
                .isEqualTo("Log: " + message);
    }

    @ParameterizedTest
    @EnumSource(LogCategory.class)
    void shouldLogAllCategoriesWithException(LogCategory category) {
        // Given
        String message = "Test exception message";
        // When
        GalaxyLogger.error(category, message, new RuntimeException("Test exception"));
        assertThat(appender.getEvents()).hasSize(1);
        Map<String, String> contextMap = appender.getEvents().get(0).getContextData().toMap();
        assertThat(contextMap)
                .containsEntry("log_category", category.name())
                .containsEntry("request_method", "POST")
                .containsEntry("request_uri", "/api/test")
                .containsEntry("traceId", traceContext.getTraceId())
                .containsEntry("parentSpanId", traceContext.getParentSpanId())
                .containsEntry("spanId", traceContext.getSpanId());
        // 验证traceId的格式 (32位十六进制字符)
        assertThat(contextMap.get("traceId"))
                .matches("[0-9a-f]{32}");
        // 验证spanId的格式 (16位十六进制字符)
        assertThat(contextMap.get("spanId"))
                .matches("[0-9a-f]{16}");
        // 验证Message
        assertThat(appender.getEvents().get(0).getMessage().getFormattedMessage())
                .isEqualTo(message);
        // 验证Exception
        assertThat(appender.getEvents().get(0).getThrown())
                .isInstanceOf(RuntimeException.class)
                .hasMessage("Test exception");
    }

    @ParameterizedTest
    @MethodSource("logMethodProvider")
    void shouldLogAllFieldsCorrectWithDefaultCategory(LogMethod logMethod) {
        // Given
        String message = "Test message";
        // When
        logMethod.log("Log: {}", message);
        // Then
        assertThat(appender.getEvents()).hasSize(1);
        Map<String, String> contextMap = appender.getEvents().get(0).getContextData().toMap();
        assertThat(contextMap)
                .containsEntry("log_category", LogCategory.BUSINESS_LOG.name())
                .containsEntry("request_method", "POST")
                .containsEntry("request_uri", "/api/test")
                .containsEntry("traceId", traceContext.getTraceId())
                .containsEntry("parentSpanId", traceContext.getParentSpanId())
                .containsEntry("spanId", traceContext.getSpanId());
        // 验证traceId的格式 (32位十六进制字符)
        assertThat(contextMap.get("traceId"))
                .matches("[0-9a-f]{32}");
        // 验证spanId的格式 (16位十六进制字符)
        assertThat(contextMap.get("spanId"))
                .matches("[0-9a-f]{16}");
        // 验证Message
        assertThat(appender.getEvents().get(0).getMessage().getFormattedMessage())
                .isEqualTo("Log: " + message);
    }

    @Test
    void shouldLogAllFieldsWithDefaultCategoryWithException() {
        // Given
        String message = "Test exception message";
        // When
        GalaxyLogger.error(message, new RuntimeException("Test exception"));
        // Then
        assertThat(appender.getEvents()).hasSize(1);
        Map<String, String> contextMap = appender.getEvents().get(0).getContextData().toMap();
        assertThat(contextMap)
                .containsEntry("log_category", LogCategory.BUSINESS_LOG.name())
                .containsEntry("request_method", "POST")
                .containsEntry("request_uri", "/api/test")
                .containsEntry("traceId", traceContext.getTraceId())
                .containsEntry("parentSpanId", traceContext.getParentSpanId())
                .containsEntry("spanId", traceContext.getSpanId());
        // 验证traceId的格式 (32位十六进制字符)
        assertThat(contextMap.get("traceId"))
                .matches("[0-9a-f]{32}");
        // 验证spanId的格式 (16位十六进制字符)
        assertThat(contextMap.get("spanId"))
                .matches("[0-9a-f]{16}");
        // 验证Message
        assertThat(appender.getEvents().get(0).getMessage().getFormattedMessage())
                .isEqualTo(message);
        // 验证Exception
        assertThat(appender.getEvents().get(0).getThrown())
                .isInstanceOf(RuntimeException.class)
                .hasMessage("Test exception");
    }

    @ParameterizedTest
    @MethodSource("logMethodMultipleCategoryProvider")
    void shouldEscapePipeCharacterInMessage(LogMethodWithCategory logMethod, LogCategory category) {
        String originalMessage = "This is a test message with a | character.";
        String expectedMessage = "This is a test message with a %7C character.";

        logMethod.log(category, "{}", originalMessage);
        assertThat(appender.getEvents().get(0).getMessage().getFormattedMessage())
                .isEqualTo(expectedMessage);
    }

    @ParameterizedTest
    @EnumSource(LogCategory.class)
    void shouldEscapePipeCharacterInMessageWithException(LogCategory category) {
        String originalMessage = "This is a test message with a | character.";
        String expectedMessage = "This is a test message with a %7C character.";

        GalaxyLogger.error(category, originalMessage, new RuntimeException("Test Exception"));
        assertThat(appender.getEvents().get(0).getMessage().getFormattedMessage())
                .isEqualTo(expectedMessage);
    }

    @ParameterizedTest
    @MethodSource("logMethodProvider")
    void shouldEscapePipeCharacterInMessageWithDefaultCategory(LogMethod logMethod) {
        String originalMessage = "This is a test message with a | character.";
        String expectedMessage = "This is a test message with a %7C character.";

        logMethod.log("{}", originalMessage);
        assertThat(appender.getEvents().get(0).getMessage().getFormattedMessage())
                .isEqualTo(expectedMessage);
    }

    @Test
    void shouldEscapePipeCharacterInMessageWithDefaultCategoryWithException() {
        String originalMessage = "This is a test message with a | character.";
        String expectedMessage = "This is a test message with a %7C character.";

        GalaxyLogger.error(originalMessage, new RuntimeException("Test Exception"));
        assertThat(appender.getEvents().get(0).getMessage().getFormattedMessage())
                .isEqualTo(expectedMessage);
    }

    @ParameterizedTest
    @MethodSource("logMethodMultipleCategoryProvider")
    void shouldTruncateMessageExceedingMaxLengthWithArgs(LogMethodWithCategory logMethod, LogCategory category) {
        // Given
        LogContext.current().getLogProperties().setMaxLength(50);
        String longMessage = "This is a very long message that should be truncated.";
        String expectedMessage = "This is a very long message that should be truncat" + IGalaxyLogger.TRUNCATED;

        // When
        logMethod.log(category, "{}", longMessage);

        // Then
        assertThat(appender.getEvents().get(0).getMessage().getFormattedMessage())
                .hasSize(50 + IGalaxyLogger.TRUNCATED.length())
                .isEqualTo(expectedMessage);
    }

    @ParameterizedTest
    @EnumSource(LogCategory.class)
    void shouldTruncateMessageExceedingMaxLengthWithException(LogCategory category) {
        // Given
        LogContext.current().getLogProperties().setMaxLength(50);
        String longMessage = "This is a very long message that should be truncated.";
        String expectedMessage = "This is a very long message that should be truncat" + IGalaxyLogger.TRUNCATED;

        GalaxyLogger.error(category, longMessage, new RuntimeException("Test Exception"));
        assertThat(appender.getEvents().get(0).getMessage().getFormattedMessage())
                .hasSize(50 + IGalaxyLogger.TRUNCATED.length())
                .isEqualTo(expectedMessage);
    }

    @ParameterizedTest
    @MethodSource("logMethodProvider")
    void shouldTruncateMessageExceedingMaxLengthWithArgsAndDefaultCategory(LogMethod logMethod) {
        // Given
        LogContext.current().getLogProperties().setMaxLength(50);
        String longMessage = "This is a very long message that should be truncated.";
        String expectedMessage = "This is a very long message that should be truncat" + IGalaxyLogger.TRUNCATED;

        // When
        logMethod.log("{}", longMessage);

        // Then
        assertThat(appender.getEvents().get(0).getMessage().getFormattedMessage())
                .hasSize(50 + IGalaxyLogger.TRUNCATED.length())
                .isEqualTo(expectedMessage);
    }

    @Test
    void shouldTruncateMessageExceedingMaxLengthWithDefaultCategoryWithException() {
        // Given
        LogContext.current().getLogProperties().setMaxLength(50);
        String longMessage = "This is a very long message that should be truncated.";
        String expectedMessage = "This is a very long message that should be truncat" + IGalaxyLogger.TRUNCATED;

        GalaxyLogger.error(longMessage, new RuntimeException("Test Exception"));
        assertThat(appender.getEvents().get(0).getMessage().getFormattedMessage())
                .hasSize(50 + IGalaxyLogger.TRUNCATED.length())
                .isEqualTo(expectedMessage);
    }

    @ParameterizedTest
    @EnumSource(LogCategory.class)
    void shouldNotPrintPrettyExceptionWithCategoryWhenDisabled(LogCategory category) {
        // 设置配置为 false
        System.setProperty(PropertyConstants.SYS_PROPERTY_EXCEPTION_PRETTY_PRINT, "false");

        // 获取LogContext使用的logger名称（GalaxyLogger的类名）
        String loggerName = "cn.com.chinastock.cnf.core.log.GalaxyLogger";
        
        // 获取Log4j2的LoggerContext
        LoggerContext context = (LoggerContext) LogManager.getContext(false);
        
        // 获取对应的Log4j2 logger
        Logger log4jLogger = context.getLogger(loggerName);
        
        // 移除已有的Appender
        for (Appender appender : log4jLogger.getAppenders().values()) {
            log4jLogger.removeAppender(appender);
        }

        // 添加测试Appender - 使用普通ThresholdFilter来捕获所有日志
        TestLogAppender exceptionAppender = new TestLogAppender(ThresholdFilter.createFilter(Level.ALL, Filter.Result.ACCEPT, Filter.Result.DENY));
        exceptionAppender.start();
        log4jLogger.addAppender(exceptionAppender);
        log4jLogger.setLevel(Level.ALL);
        log4jLogger.setAdditive(false);

        // 创建并记录一个异常日志
        GalaxyLogger.error(category, "Test exception", new RuntimeException("Test exception"));

        // 验证日志被记录，但没有pretty print（因为pretty print被禁用）
        assertThat(exceptionAppender.getEvents())
                .hasSize(1)
                .allSatisfy(event -> {
                    String message = event.getMessage().getFormattedMessage();
                    assertThat(message).contains("Test exception");
                    assertThat(event.getThrown()).isNotNull();
                    assertThat(event.getThrown().getMessage()).isEqualTo("Test exception");
                    // 验证没有pretty print格式（消息应该保持原样）
                    assertThat(message).doesNotContain("Pretty Print");
                });
    }

    @ParameterizedTest
    @EnumSource(LogCategory.class)
    void shouldPrintPrettyExceptionWithCategoryWhenEnabled(LogCategory category) {
        // 设置配置为 true
        System.setProperty(PropertyConstants.SYS_PROPERTY_EXCEPTION_PRETTY_PRINT, "true");

        // 获取LogContext使用的logger名称（GalaxyLogger的类名）
        String loggerName = "cn.com.chinastock.cnf.core.log.GalaxyLogger";
        
        // 获取Log4j2的LoggerContext
        LoggerContext context = (LoggerContext) LogManager.getContext(false);
        
        // 获取对应的Log4j2 logger
        Logger log4jLogger = context.getLogger(loggerName);
        
        // 移除已有的Appender
        for (Appender appender : log4jLogger.getAppenders().values()) {
            log4jLogger.removeAppender(appender);
        }

        // 添加测试Appender - 使用ThrowablePrettyPrintFilter来捕获pretty print日志
        TestLogAppender exceptionAppender = new TestLogAppender(ThrowablePrettyPrintFilter.createFilter(Filter.Result.ACCEPT, Filter.Result.DENY));
        exceptionAppender.start();
        log4jLogger.addAppender(exceptionAppender);
        log4jLogger.setLevel(Level.ALL);
        log4jLogger.setAdditive(false);

        // 创建并记录一个异常日志
        GalaxyLogger.error(category, "Test error", new RuntimeException("Test exception"));

        // 验证pretty print日志记录
        assertThat(exceptionAppender.getEvents())
                .hasSize(1)
                .allSatisfy(event -> {
                    String message = event.getMessage().getFormattedMessage();
                    assertThat(message).contains("Test error");
                    assertThat(event.getThrown()).isNotNull();
                    assertThat(event.getThrown().getMessage()).isEqualTo("Test exception");
                });
    }

    @Test
    void shouldNotPrintPrettyExceptionWithDefaultCategoryWhenDisabled() {
        // 设置配置为 false
        System.setProperty(PropertyConstants.SYS_PROPERTY_EXCEPTION_PRETTY_PRINT, "false");

        // 获取LogContext使用的logger名称（GalaxyLogger的类名）
        String loggerName = "cn.com.chinastock.cnf.core.log.GalaxyLogger";
        
        // 获取Log4j2的LoggerContext
        LoggerContext context = (LoggerContext) LogManager.getContext(false);
        
        // 获取对应的Log4j2 logger
        Logger log4jLogger = context.getLogger(loggerName);
        
        // 移除已有的Appender
        for (Appender appender : log4jLogger.getAppenders().values()) {
            log4jLogger.removeAppender(appender);
        }

        // 添加测试Appender - 使用普通ThresholdFilter来捕获所有日志
        TestLogAppender exceptionAppender = new TestLogAppender(ThresholdFilter.createFilter(Level.ALL, Filter.Result.ACCEPT, Filter.Result.DENY));
        exceptionAppender.start();
        log4jLogger.addAppender(exceptionAppender);
        log4jLogger.setLevel(Level.ALL);
        log4jLogger.setAdditive(false);

        // 创建并记录一个异常日志
        GalaxyLogger.error("Test exception", new RuntimeException("Test exception"));

        // 验证日志被记录，但没有pretty print（因为pretty print被禁用）
        assertThat(exceptionAppender.getEvents())
                .hasSize(1)
                .allSatisfy(event -> {
                    String message = event.getMessage().getFormattedMessage();
                    assertThat(message).contains("Test exception");
                    assertThat(event.getThrown()).isNotNull();
                    assertThat(event.getThrown().getMessage()).isEqualTo("Test exception");
                    // 验证没有pretty print格式（消息应该保持原样）
                    assertThat(message).doesNotContain("Pretty Print");
                });
    }

    @Test
    void shouldPrintPrettyExceptionWithDefaultCategoryWhenEnabled() {
        // 设置配置为 true
        System.setProperty(PropertyConstants.SYS_PROPERTY_EXCEPTION_PRETTY_PRINT, "true");

        // 获取LogContext使用的logger名称（GalaxyLogger的类名）
        String loggerName = "cn.com.chinastock.cnf.core.log.GalaxyLogger";
        
        // 获取Log4j2的LoggerContext
        LoggerContext context = (LoggerContext) LogManager.getContext(false);
        
        // 获取对应的Log4j2 logger
        Logger log4jLogger = context.getLogger(loggerName);
        
        // 移除已有的Appender
        for (Appender appender : log4jLogger.getAppenders().values()) {
            log4jLogger.removeAppender(appender);
        }

        // 添加测试Appender - 使用ThrowablePrettyPrintFilter来捕获pretty print日志
        TestLogAppender exceptionAppender = new TestLogAppender(ThrowablePrettyPrintFilter.createFilter(Filter.Result.ACCEPT, Filter.Result.DENY));
        exceptionAppender.start();
        log4jLogger.addAppender(exceptionAppender);
        log4jLogger.setLevel(Level.ALL);
        log4jLogger.setAdditive(false);

        // 创建并记录一个异常日志
        GalaxyLogger.error("Test error", new RuntimeException("Test exception"));

        // 验证pretty print日志记录
        assertThat(exceptionAppender.getEvents())
                .hasSize(1)
                .allSatisfy(event -> {
                    String message = event.getMessage().getFormattedMessage();
                    assertThat(message).contains("Test error");
                    assertThat(event.getThrown()).isNotNull();
                    assertThat(event.getThrown().getMessage()).isEqualTo("Test exception");
                });
    }
}
