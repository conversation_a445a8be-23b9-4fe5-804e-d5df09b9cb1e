package cn.com.chinastock.cnf.core.log.aspect;

import cn.com.chinastock.cnf.core.log.config.LogProperties;
import cn.com.chinastock.cnf.core.log.logger.FrameworkLogger;
import javax.servlet.http.HttpServletResponse;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.reflect.MethodSignature;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.MethodParameter;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.converter.json.JsonbHttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServletServerHttpResponse;
import org.springframework.web.bind.annotation.RequestBody;

import java.lang.reflect.Method;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ControllerLogAspectTest {

    @Mock
    private ProceedingJoinPoint joinPoint;

    @Mock
    private MethodSignature methodSignature;

    @Mock
    private ServerHttpRequest serverHttpRequest;

    @Mock
    private ServletServerHttpResponse serverHttpResponse;

    @Mock
    private HttpServletResponse response;

    @Spy
    private LogProperties logProperties;

    @Mock
    private FrameworkLogger frameworkLogger;

    private ControllerLogAspect controllerLogAspect;

    @BeforeEach
    void setUp() throws Exception {
        // 设置 LogProperties
        logProperties.setRequestResponseEnabled(true);
        logProperties.setRequestResponseMaskFieldEnabled(true);

        // 创建测试对象
        controllerLogAspect = new ControllerLogAspect(logProperties);

        // 通过反射注入 mock 的 frameworkLogger
        java.lang.reflect.Field field = ControllerLogAspect.class.getDeclaredField("frameworkLogger");
        field.setAccessible(true);
        field.set(controllerLogAspect, frameworkLogger);
    }

    @Test
    void shouldLogRequestWhenControllerLogEnabled() throws Throwable {
        // Given
        TestRequest testRequest = new TestRequest("testUser", "password123");
        Object[] args = new Object[]{testRequest};
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.getSignature()).thenReturn(methodSignature);

        Method method = TestController.class.getDeclaredMethod("testMethod", TestRequest.class);
        when(methodSignature.getMethod()).thenReturn(method);

        // When
        controllerLogAspect.around(joinPoint);

        // Then
        verify(frameworkLogger).logRequestWithFieldsMasked(testRequest);
        verify(joinPoint).proceed();
    }

    @Test
    void shouldLogResponseWhenControllerLogEnabled() {
        // Given
        TestResponse testResponse = new TestResponse("testName", "sensitiveInfo");
        when(serverHttpResponse.getServletResponse()).thenReturn(response);
        when(serverHttpResponse.getHeaders()).thenReturn(new HttpHeaders());
        when(response.getStatus()).thenReturn(200);

        // When
        controllerLogAspect.beforeBodyWrite(
                testResponse,
                mock(MethodParameter.class),
                MediaType.APPLICATION_JSON,
                JsonbHttpMessageConverter.class,
                serverHttpRequest,
                serverHttpResponse
        );

        // Then
        verify(frameworkLogger).logResponseWithFieldsMasked(
                eq(MediaType.APPLICATION_JSON),
                any(HttpHeaders.class),
                anyInt(),
                eq(testResponse)
        );
    }

    // Test classes
    static class TestController {
        public void testMethod(@RequestBody TestRequest request) {
        }
    }

    static class TestRequest {
        private String username;
        @MaskedField
        private String password;

        public TestRequest(String username, String password) {
            this.username = username;
            this.password = password;
        }
    }

    static class TestResponse {
        private String name;
        @MaskedField
        private String sensitiveData;

        public TestResponse(String name, String sensitiveData) {
            this.name = name;
            this.sensitiveData = sensitiveData;
        }
    }
} 