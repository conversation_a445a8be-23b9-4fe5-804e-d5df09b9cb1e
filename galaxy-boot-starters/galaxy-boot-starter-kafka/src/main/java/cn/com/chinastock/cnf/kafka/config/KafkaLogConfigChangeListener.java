package cn.com.chinastock.cnf.kafka.config;

import cn.com.chinastock.cnf.core.log.GalaxyLogger;
import cn.com.chinastock.cnf.core.log.GalaxyLoggerFactory;
import cn.com.chinastock.cnf.core.log.IGalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import com.ctrip.framework.apollo.core.dto.ApolloConfig;
import com.ctrip.framework.apollo.model.ConfigChangeEvent;
import javax.annotation.PostConstruct;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.annotation.Configuration;

/**
 * Kafka日志配置变更监听器
 * 监听Apollo配置变更事件，动态更新Kafka日志拦截器配置
 * <p>
 * 使用方式：
 * 1. 确保项目中引入了Apollo客户端依赖
 * 2. 在Apollo中修改 galaxy.kafka.log.enabled 或 galaxy.kafka.log.max-batch-detail-count
 * 3. 配置会实时生效，无需重启应用
 *
 * <AUTHOR>
 */
@Configuration
@ConditionalOnClass(ApolloConfig.class)
public class KafkaLogConfigChangeListener {

    IGalaxyLogger galaxyLogger = GalaxyLoggerFactory.getLogger(KafkaLogConfigChangeListener.class);

    private final DynamicKafkaLogManager dynamicKafkaLogManager;

    public KafkaLogConfigChangeListener(DynamicKafkaLogManager dynamicKafkaLogManager) {
        this.dynamicKafkaLogManager = dynamicKafkaLogManager;
    }

    @PostConstruct
    public void initialize() {
        GalaxyLogger.info(LogCategory.FRAMEWORK_LOG, "KafkaLogConfigChangeListener initialized");
        Config config = ConfigService.getAppConfig();

        // 初始化时直接从Apollo读取配置，确保配置正确
        initializeFromApolloConfig(config);

        config.addChangeListener(this::onChange);
    }

    /**
     * 从Apollo配置初始化动态管理器
     * @param config Apollo配置对象
     */
    private void initializeFromApolloConfig(Config config) {
        try {
            boolean enabled = config.getBooleanProperty("galaxy.kafka.log.enabled", true);
            int maxDetailRecords = config.getIntProperty("galaxy.kafka.log.max-batch-detail-count", 5);

            dynamicKafkaLogManager.updateLogEnabled(enabled);
            dynamicKafkaLogManager.updateMaxDetailRecordsCount(maxDetailRecords);

            galaxyLogger.info(LogCategory.FRAMEWORK_LOG,
                    "KafkaLogConfigChangeListener: initialized with configuration: {}",
                    dynamicKafkaLogManager.getConfigStatus());
        } catch (Exception e) {
            galaxyLogger.error(LogCategory.EXCEPTION_LOG,
                    "KafkaLogConfigChangeListener: Failed to initialize from Apollo config", e);
            // 如果Apollo配置读取失败，回退到从Properties同步
            dynamicKafkaLogManager.syncFromProperties();
        }
    }


    void onChange(ConfigChangeEvent changeEvent) {
        try {
            galaxyLogger.info(LogCategory.FRAMEWORK_LOG,
                    "KafkaLogConfigChangeListener: log configuration change detected, updating dynamic settings...");

            // 检查是否是Kafka日志相关的配置变更
            boolean hasKafkaLogChanges = changeEvent.changedKeys().stream()
                    .anyMatch(key -> key.startsWith("galaxy.kafka.log."));

            if (hasKafkaLogChanges) {
                // 直接从Apollo配置中读取最新值，避免时机问题
                Config config = ConfigService.getAppConfig();
                boolean enabled = config.getBooleanProperty("galaxy.kafka.log.enabled", true);
                int maxDetailRecords = config.getIntProperty("galaxy.kafka.log.max-batch-detail-count", 5);

                // 直接更新动态管理器，而不是通过syncFromProperties
                dynamicKafkaLogManager.updateLogEnabled(enabled);
                dynamicKafkaLogManager.updateMaxDetailRecordsCount(maxDetailRecords);

                galaxyLogger.info(LogCategory.FRAMEWORK_LOG,
                        "KafkaLogConfigChangeListener: log configuration updated: {}",
                        dynamicKafkaLogManager.getConfigStatus());
                galaxyLogger.info(LogCategory.FRAMEWORK_LOG,
                        "KafkaLogConfigChangeListener: logDisabled={}", DynamicKafkaLogManager.isLogDisabled());
            } else {
                galaxyLogger.debug(LogCategory.FRAMEWORK_LOG,
                        "KafkaLogConfigChangeListener: configuration change detected but no Kafka log settings affected");
            }

        } catch (Exception e) {
            galaxyLogger.error(LogCategory.EXCEPTION_LOG,
                    "KafkaLogConfigChangeListener:Failed to handle Kafka log configuration change", e);
        }
    }
}
