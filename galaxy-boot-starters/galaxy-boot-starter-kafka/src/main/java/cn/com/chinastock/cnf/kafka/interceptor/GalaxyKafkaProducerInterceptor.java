package cn.com.chinastock.cnf.kafka.interceptor;

import cn.com.chinastock.cnf.core.log.GalaxyLoggerFactory;
import cn.com.chinastock.cnf.core.log.IGalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import cn.com.chinastock.cnf.core.log.utils.TraceUtils;
import cn.com.chinastock.cnf.kafka.config.DynamicKafkaLogManager;
import cn.com.chinastock.cnf.kafka.utils.KafkaLogUtils;
import org.apache.kafka.clients.producer.ProducerInterceptor;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.clients.producer.RecordMetadata;
import org.apache.kafka.common.header.Header;
import org.slf4j.MDC;

import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

import static cn.com.chinastock.cnf.core.log.context.TraceConstants.*;
import static cn.com.chinastock.cnf.kafka.utils.IdempotencyUtils.IDEMPOTENCY_ID;

/**
 * Galaxy Kafka Producer拦截器
 * 用于拦截Kafka消息发送事件并记录日志
 * 支持通过DynamicKafkaLogManager进行动态配置更新
 * <p>
 * 新的 trace 信息处理方案：
 * 1. 从 traceparent header 中解析 trace 信息
 * 2. 在 ack 和 callback 线程中重新生成 spanId 并打印日志
 * 3. 添加幂等性 ID 到消息 header 中，供 Consumer 进行重试和避免重复消费
 *
 * @param <K> 消息键的类型
 * @param <V> 消息值的类型
 * <AUTHOR>
 */
public class GalaxyKafkaProducerInterceptor<K, V> implements ProducerInterceptor<K, V> {

    private static final IGalaxyLogger galaxyLogger = GalaxyLoggerFactory.getLogger(GalaxyKafkaProducerInterceptor.class);

    /**
     * 存储消息 ID 到 traceparent 的映射，用于在 callback 中恢复
     * 使用轻量级的消息 ID 作为 key，减少内存占用
     * Key: 内部消息 ID
     * Value: traceparent 字符串和时间戳
     */
    private final Map<String, TraceparentInfo> messageTraceparentMap = new ConcurrentHashMap<>();

    @Override
    public ProducerRecord<K, V> onSend(ProducerRecord<K, V> record) {
        if (record == null) {
            return record;
        }

        try {
            // 1. 添加幂等性 ID
            ProducerRecord<K, V> recordWithIdempotency = addIdempotencyId(record);

            // 2. 添加或更新 traceparent header
            ProducerRecord<K, V> finalRecord = addOrUpdateTraceparent(recordWithIdempotency);

            // 3. 保存 traceparent 信息用于 callback 恢复
            saveTraceparentForCallback(finalRecord);

            // 4. 记录发送日志
            if (!DynamicKafkaLogManager.isLogDisabled()) {
                StringBuilder logMessage = KafkaLogUtils.constructProducerSendLogMessage(finalRecord);
                galaxyLogger.info(LogCategory.FRAMEWORK_LOG, "Kafka Producer Send: {}", logMessage.toString());
            }

            return finalRecord;
        } catch (Exception e) {
            galaxyLogger.error(LogCategory.EXCEPTION_LOG, "Failed to process Kafka producer send event", e);
            return record;
        }
    }

    @Override
    public void onAcknowledgement(RecordMetadata metadata, Exception exception) {
        try {
            // 1. 从保存的信息中恢复 traceparent 并解析到 MDC
            restoreTraceparentFromCallback(metadata);

            // 2. 记录 ack 日志（使用原有的 trace 信息，不重新生成 spanId）
            if (!DynamicKafkaLogManager.isLogDisabled()) {
                logProducerAck(metadata, exception);
            }
        } catch (Exception e) {
            galaxyLogger.error(LogCategory.EXCEPTION_LOG, "Failed to log Kafka producer acknowledgement event", e);
        } finally {
            // 3. 清理 traceparent 信息和 MDC
            cleanupTraceparentForCallback(metadata);
            clearMDC();
        }
    }

    private void logProducerAck(RecordMetadata metadata, Exception exception) {
        if (metadata != null) {
            StringBuilder logMessage = KafkaLogUtils.constructProducerAckLogMessage(metadata);
            galaxyLogger.info(LogCategory.FRAMEWORK_LOG, "Kafka Producer Ack: {}", logMessage.toString());
        } else if (exception != null) {
            StringBuilder logMessage = KafkaLogUtils.constructProducerAckFailedLogMessage(exception);
            galaxyLogger.error(LogCategory.FRAMEWORK_LOG, "Kafka Producer Ack Failed: {}", logMessage.toString());
        }
    }

    @Override
    public void close() {
        // 清理所有 traceparent 信息
        try {
            messageTraceparentMap.clear();
        } catch (Exception e) {
            galaxyLogger.debug(LogCategory.FRAMEWORK_LOG, "Failed to clear traceparent map on close", e);
        }
    }

    @Override
    public void configure(Map<String, ?> configs) {
        // 配置方法，目前不需要特殊配置
        galaxyLogger.debug(LogCategory.FRAMEWORK_LOG, "GalaxyKafkaProducerInterceptor configured");
    }

    /**
     * 生成幂等性 ID
     *
     * @return 唯一的幂等性 ID
     */
    private String generateIdempotencyId() {
        return UUID.randomUUID().toString();
    }

    /**
     * 为 ProducerRecord 添加幂等性 ID
     *
     * @param record 原始的 ProducerRecord
     * @return 添加了幂等性 ID 的 ProducerRecord
     */
    private ProducerRecord<K, V> addIdempotencyId(ProducerRecord<K, V> record) {
        if (record == null) {
            return record;
        }

        try {
            String idempotencyId = generateIdempotencyId();

            // 创建新的 ProducerRecord 并添加幂等性 ID header
            ProducerRecord<K, V> newRecord = new ProducerRecord<>(
                    record.topic(),
                    record.partition(),
                    record.timestamp(),
                    record.key(),
                    record.value(),
                    record.headers()
            );

            // 添加幂等性 ID header
            newRecord.headers().add(IDEMPOTENCY_ID, idempotencyId.getBytes(StandardCharsets.UTF_8));
            return newRecord;
        } catch (Exception e) {
            galaxyLogger.debug(LogCategory.FRAMEWORK_LOG, "Failed to add idempotency ID to record", e);
            return record;
        }
    }

    /**
     * 添加或更新 traceparent header
     *
     * @param record 原始的 ProducerRecord
     * @return 添加了 traceparent header 的 ProducerRecord
     */
    private ProducerRecord<K, V> addOrUpdateTraceparent(ProducerRecord<K, V> record) {
        if (record == null) {
            return record;
        }

        try {
            // 从 MDC 获取 trace 信息
            String traceId = MDC.get(TRACE_ID);
            String spanId = MDC.get(SPAN_ID);

            if (traceId != null && spanId != null) {
                // 构建 W3C traceparent header
                String traceparent = TraceUtils.generateTraceParent(traceId, spanId);

                // 创建新的 ProducerRecord 并添加 traceparent header
                ProducerRecord<K, V> newRecord = new ProducerRecord<>(
                        record.topic(),
                        record.partition(),
                        record.timestamp(),
                        record.key(),
                        record.value(),
                        record.headers()
                );

                // 先移除已存在的 traceparent header，避免重复
                newRecord.headers().remove(TRACE_PARENT);
                // 添加新的 traceparent header
                newRecord.headers().add(TRACE_PARENT, traceparent.getBytes(StandardCharsets.UTF_8));
                return newRecord;
            }
        } catch (Exception e) {
            galaxyLogger.debug(LogCategory.FRAMEWORK_LOG, "Failed to add traceparent header to record", e);
        }

        return record;
    }


    /**
     * 保存 traceparent 信息用于 callback 恢复
     * 使用 topic-partition-timestamp 作为 key，这样可以在 callback 中精确匹配
     *
     * @param record ProducerRecord
     */
    private void saveTraceparentForCallback(ProducerRecord<K, V> record) {
        if (record == null) {
            return;
        }

        try {
            // 从 record headers 中获取 traceparent
            String traceparent = null;
            if (record.headers() != null) {
                Header traceparentHeader = record.headers().lastHeader(TRACE_PARENT);
                if (traceparentHeader != null) {
                    traceparent = new String(traceparentHeader.value(), StandardCharsets.UTF_8);
                }
            }

            // 如果有 traceparent，保存到 map 中
            if (traceparent != null) {
                // 使用 topic-partition-timestamp 作为 key
                // 注意：在 onSend 时，partition 可能为 null，timestamp 也可能为 null
                String key = buildMessageKey(record.topic(), record.partition(), record.timestamp());
                TraceparentInfo info = new TraceparentInfo(traceparent, System.currentTimeMillis());
                messageTraceparentMap.put(key, info);

                galaxyLogger.debug(LogCategory.FRAMEWORK_LOG,
                        "Saved traceparent for key: {}, traceparent: {}", key, traceparent);

                // 定期清理过期的信息
                cleanupExpiredTraceparentInfo();
            }
        } catch (Exception e) {
            galaxyLogger.debug(LogCategory.FRAMEWORK_LOG, "Failed to save traceparent for callback", e);
        }
    }

    /**
     * 构建消息的唯一标识 key
     *
     * @param topic     主题
     * @param partition 分区（可能为 null）
     * @param timestamp 时间戳（可能为 null）
     * @return 消息 key
     */
    private String buildMessageKey(String topic, Integer partition, Long timestamp) {
        StringBuilder keyBuilder = new StringBuilder();
        keyBuilder.append(topic);

        if (partition != null) {
            keyBuilder.append("-p").append(partition);
        } else {
            keyBuilder.append("-p?");
        }

        if (timestamp != null) {
            keyBuilder.append("-t").append(timestamp);
        } else {
            // 如果没有 timestamp，使用当前时间的纳秒部分作为区分
            keyBuilder.append("-t").append(System.nanoTime());
        }

        return keyBuilder.toString();
    }

    /**
     * 从 callback 中恢复 traceparent 并解析到 MDC
     * 使用多级匹配策略：精确匹配 -> 主题分区匹配 -> 主题匹配 -> 最近时间匹配
     *
     * @param metadata RecordMetadata
     */
    private void restoreTraceparentFromCallback(RecordMetadata metadata) {
        if (metadata == null) {
            return;
        }

        try {
            TraceparentInfo traceparentInfo = findTraceparentInfo(metadata);
            if (traceparentInfo != null) {
                parseTraceparentToMDC(traceparentInfo.getTraceparent());
                galaxyLogger.debug(LogCategory.FRAMEWORK_LOG,
                        "Restored traceparent for topic: {}, partition: {}, offset: {}",
                        metadata.topic(), metadata.partition(), metadata.offset());
            } else {
                galaxyLogger.debug(LogCategory.FRAMEWORK_LOG,
                        "No traceparent found for topic: {}, partition: {}, offset: {}",
                        metadata.topic(), metadata.partition(), metadata.offset());
            }
        } catch (Exception e) {
            galaxyLogger.debug(LogCategory.FRAMEWORK_LOG, "Failed to restore traceparent from callback", e);
        }
    }

    /**
     * 查找匹配的 traceparent 信息
     * 使用多级匹配策略提高准确性
     *
     * @param metadata RecordMetadata
     * @return TraceparentInfo 或 null
     */
    private TraceparentInfo findTraceparentInfo(RecordMetadata metadata) {
        if (messageTraceparentMap.isEmpty()) {
            return null;
        }

        // 策略1：尝试精确匹配 topic-partition-timestamp
        if (metadata.timestamp() >= 0) {
            String exactKey = buildCallbackKey(metadata.topic(), metadata.partition(), metadata.timestamp());
            TraceparentInfo exactMatch = messageTraceparentMap.get(exactKey);
            if (exactMatch != null) {
                galaxyLogger.debug(LogCategory.FRAMEWORK_LOG, "Found exact match for key: {}", exactKey);
                return exactMatch;
            }
        }

        // 策略2：匹配 topic-partition，选择最近的时间戳
        String topicPartitionPrefix = metadata.topic() + "-p" + metadata.partition();
        TraceparentInfo topicPartitionMatch = messageTraceparentMap.entrySet().stream()
                .filter(entry -> entry.getKey().startsWith(topicPartitionPrefix))
                .map(Map.Entry::getValue)
                .max((info1, info2) -> Long.compare(info1.getTimestamp(), info2.getTimestamp()))
                .orElse(null);

        if (topicPartitionMatch != null) {
            galaxyLogger.debug(LogCategory.FRAMEWORK_LOG, "Found topic-partition match for: {}", topicPartitionPrefix);
            return topicPartitionMatch;
        }

        // 策略3：匹配 topic，选择最近的时间戳
        String topicPrefix = metadata.topic() + "-p";
        TraceparentInfo topicMatch = messageTraceparentMap.entrySet().stream()
                .filter(entry -> entry.getKey().startsWith(topicPrefix))
                .map(Map.Entry::getValue)
                .max((info1, info2) -> Long.compare(info1.getTimestamp(), info2.getTimestamp()))
                .orElse(null);

        if (topicMatch != null) {
            galaxyLogger.debug(LogCategory.FRAMEWORK_LOG, "Found topic match for: {}", metadata.topic());
            return topicMatch;
        }

        // 策略4：降级到最近时间匹配
        TraceparentInfo latestMatch = messageTraceparentMap.values().stream()
                .max((info1, info2) -> Long.compare(info1.getTimestamp(), info2.getTimestamp()))
                .orElse(null);

        if (latestMatch != null) {
            galaxyLogger.debug(LogCategory.FRAMEWORK_LOG, "Using latest timestamp match as fallback");
        }

        return latestMatch;
    }

    /**
     * 构建 callback 时的消息 key
     *
     * @param topic     主题
     * @param partition 分区
     * @param timestamp 时间戳
     * @return 消息 key
     */
    private String buildCallbackKey(String topic, int partition, long timestamp) {
        return topic + "-p" + partition + "-t" + timestamp;
    }

    /**
     * 解析 traceparent 字符串并设置到 MDC
     *
     * @param traceparent W3C traceparent 字符串
     */
    private void parseTraceparentToMDC(String traceparent) {
        if (traceparent == null || traceparent.trim().isEmpty()) {
            return;
        }

        try {
            String[] parts = traceparent.split("-");
            if (parts.length >= 4) {
                String traceId = parts[1];
                String parentSpanId = parts[2];

                // 设置到 MDC
                MDC.put(TRACE_ID, traceId);
                MDC.put(PARENT_SPAN_ID, parentSpanId);
                // spanId 将在 generateNewSpanIdForCallback 中重新生成
            }
        } catch (Exception e) {
            galaxyLogger.debug(LogCategory.FRAMEWORK_LOG, "Failed to parse traceparent: {}", traceparent, e);
        }
    }


    /**
     * 清理 callback 的 traceparent 信息
     * 尝试精确清理匹配的 traceparent，如果找不到则清理最老的
     *
     * @param metadata RecordMetadata
     */
    private void cleanupTraceparentForCallback(RecordMetadata metadata) {
        if (metadata == null || messageTraceparentMap.isEmpty()) {
            return;
        }

        try {
            boolean removed = false;

            // 尝试精确匹配并删除
            if (metadata.timestamp() >= 0) {
                String exactKey = buildCallbackKey(metadata.topic(), metadata.partition(), metadata.timestamp());
                if (messageTraceparentMap.remove(exactKey) != null) {
                    galaxyLogger.debug(LogCategory.FRAMEWORK_LOG, "Removed exact traceparent for key: {}", exactKey);
                    removed = true;
                }
            }

            // 如果精确匹配失败，尝试删除同一 topic-partition 的最老记录
            if (!removed) {
                String topicPartitionPrefix = metadata.topic() + "-p" + metadata.partition();
                String keyToRemove = messageTraceparentMap.entrySet().stream()
                        .filter(entry -> entry.getKey().startsWith(topicPartitionPrefix))
                        .min((entry1, entry2) -> Long.compare(entry1.getValue().getTimestamp(), entry2.getValue().getTimestamp()))
                        .map(Map.Entry::getKey)
                        .orElse(null);

                if (keyToRemove != null) {
                    messageTraceparentMap.remove(keyToRemove);
                    galaxyLogger.debug(LogCategory.FRAMEWORK_LOG, "Removed topic-partition traceparent for key: {}", keyToRemove);
                    removed = true;
                }
            }

            // 最后的降级策略：删除最老的记录
            if (!removed && !messageTraceparentMap.isEmpty()) {
                String oldestKey = messageTraceparentMap.entrySet().stream()
                        .min((entry1, entry2) -> Long.compare(entry1.getValue().getTimestamp(), entry2.getValue().getTimestamp()))
                        .map(Map.Entry::getKey)
                        .orElse(null);

                if (oldestKey != null) {
                    messageTraceparentMap.remove(oldestKey);
                    galaxyLogger.debug(LogCategory.FRAMEWORK_LOG, "Removed oldest traceparent for key: {}", oldestKey);
                }
            }
        } catch (Exception e) {
            galaxyLogger.debug(LogCategory.FRAMEWORK_LOG, "Failed to cleanup traceparent for callback", e);
        }
    }

    /**
     * 清理 MDC 中的 trace 信息
     */
    private void clearMDC() {
        try {
            MDC.remove(TRACE_ID);
            MDC.remove(PARENT_SPAN_ID);
            MDC.remove(SPAN_ID);
        } catch (Exception e) {
            galaxyLogger.debug(LogCategory.FRAMEWORK_LOG, "Failed to clear MDC", e);
        }
    }

    /**
     * 清理过期的 traceparent 信息，防止内存泄漏
     * 清理超过 5 分钟的 traceparent 信息
     */
    private void cleanupExpiredTraceparentInfo() {
        try {
            long currentTime = System.currentTimeMillis();
            long expireTime = 5 * 60 * 1000; // 5 分钟

            messageTraceparentMap.entrySet().removeIf(entry ->
                    currentTime - entry.getValue().getTimestamp() > expireTime
            );
        } catch (Exception e) {
            galaxyLogger.debug(LogCategory.FRAMEWORK_LOG, "Failed to cleanup expired traceparent info", e);
        }
    }

    /**
     * 存储 traceparent 信息和时间戳的内部类
     */
    private static class TraceparentInfo {
        private final String traceparent;
        private final long timestamp;

        public TraceparentInfo(String traceparent, long timestamp) {
            this.traceparent = traceparent;
            this.timestamp = timestamp;
        }

        public String getTraceparent() {
            return traceparent;
        }

        public long getTimestamp() {
            return timestamp;
        }
    }
}
