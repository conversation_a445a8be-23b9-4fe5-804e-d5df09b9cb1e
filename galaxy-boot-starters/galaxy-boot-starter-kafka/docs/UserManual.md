### 组件使用说明
`Galaxy Boot Starter Kafka` 是一个基于 `Spring Cloud Kafka` 的封装，提供了统一的、规范化对接银河证券认证kafka集群。

### 组件功能介绍

#### 组件启用
当配置`galaxy.kafka.enable` 为 `true`时将启用该组件。
当配置`galaxy.kafka.jaas.enable` 为 `true`时自动设置认证模式为sasl_plaintext。
  `username`和`password`需要在`application.yml`中配置：
  ```yaml
  galaxy:
    kafka:
      enable: true
      jaas:
        enable: true
      username: XXXXXXXXXX
      password: XXXXXXXXXX
  ```