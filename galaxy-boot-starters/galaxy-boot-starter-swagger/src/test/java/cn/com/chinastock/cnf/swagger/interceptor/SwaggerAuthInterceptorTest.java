package cn.com.chinastock.cnf.swagger.interceptor;

import cn.com.chinastock.cnf.swagger.properties.SwaggerProperties;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.*;

class SwaggerAuthInterceptorTest {

    @Test
    void should_return_false_when_authorization_header_is_missing() {
        // Given
        SwaggerProperties swaggerProperties = mock(SwaggerProperties.class);
        SwaggerAuthInterceptor interceptor = new SwaggerAuthInterceptor(swaggerProperties);
        HttpServletRequest request = mock(HttpServletRequest.class);
        HttpServletResponse response = mock(HttpServletResponse.class);

        when(request.getHeader("Authorization")).thenReturn(null);

        // When
        boolean result = interceptor.preHandle(request, response, null);

        // Then
        assertThat(result).isFalse();
        verify(response).setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        verify(response).setHeader("WWW-Authenticate", "Basic realm=\"Galaxy Swagger UI\"");
    }

    @Test
    void should_return_false_when_authorization_header_is_invalid() {
        // Given
        SwaggerProperties swaggerProperties = mock(SwaggerProperties.class);
        SwaggerAuthInterceptor interceptor = new SwaggerAuthInterceptor(swaggerProperties);
        HttpServletRequest request = mock(HttpServletRequest.class);
        HttpServletResponse response = mock(HttpServletResponse.class);

        when(request.getHeader("Authorization")).thenReturn("InvalidHeader");

        // When
        boolean result = interceptor.preHandle(request, response, null);

        // Then
        assertThat(result).isFalse();
        verify(response).setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        verify(response).setHeader("WWW-Authenticate", "Basic realm=\"Galaxy Swagger UI\"");
    }

    @Test
    void should_return_false_when_credentials_are_invalid() {
        // Given
        SwaggerProperties swaggerProperties = mock(SwaggerProperties.class);
        SwaggerProperties.Auth auth = mock(SwaggerProperties.Auth.class);
        when(swaggerProperties.getAuth()).thenReturn(auth);
        when(auth.getUsername()).thenReturn("user");
        when(auth.getPassword()).thenReturn("password");

        SwaggerAuthInterceptor interceptor = new SwaggerAuthInterceptor(swaggerProperties);
        HttpServletRequest request = mock(HttpServletRequest.class);
        HttpServletResponse response = mock(HttpServletResponse.class);

        String encodedCredentials = "Basic " + java.util.Base64.getEncoder().encodeToString("wrong:wrong".getBytes());
        when(request.getHeader("Authorization")).thenReturn(encodedCredentials);

        // When
        boolean result = interceptor.preHandle(request, response, null);

        // Then
        assertThat(result).isFalse();
        verify(response).setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        verify(response).setHeader("WWW-Authenticate", "Basic realm=\"Galaxy Swagger UI\"");
    }

    @Test
    void should_return_true_when_credentials_are_valid() {
        // Given
        SwaggerProperties swaggerProperties = mock(SwaggerProperties.class);
        SwaggerProperties.Auth auth = mock(SwaggerProperties.Auth.class);
        when(swaggerProperties.getAuth()).thenReturn(auth);
        when(auth.getUsername()).thenReturn("user");
        when(auth.getPassword()).thenReturn("password");

        SwaggerAuthInterceptor interceptor = new SwaggerAuthInterceptor(swaggerProperties);
        HttpServletRequest request = mock(HttpServletRequest.class);
        HttpServletResponse response = mock(HttpServletResponse.class);

        String encodedCredentials = "Basic " + java.util.Base64.getEncoder().encodeToString("user:password".getBytes());
        when(request.getHeader("Authorization")).thenReturn(encodedCredentials);

        // When
        boolean result = interceptor.preHandle(request, response, null);

        // Then
        assertThat(result).isTrue();
    }
}
