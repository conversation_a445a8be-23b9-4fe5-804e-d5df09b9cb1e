package cn.com.chinastock.cnf.swagger.interceptor;

import cn.com.chinastock.cnf.core.log.GalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import cn.com.chinastock.cnf.swagger.properties.SwaggerProperties;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.HandlerInterceptor;

import java.nio.charset.StandardCharsets;

import static java.util.Base64.getDecoder;

public class SwaggerAuthInterceptor implements HandlerInterceptor {
    private static final String AUTHORIZATION_HEADER = "Authorization";
    private static final String BASIC_PREFIX = "Basic ";
    private static final String AUTHENTICATION_SCHEME = "Basic realm=\"Galaxy Swagger UI\"";

    private final SwaggerProperties swaggerProperties;

    public SwaggerAuthInterceptor(SwaggerProperties swaggerProperties) {
        this.swaggerProperties = swaggerProperties;
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        String auth = request.getHeader(AUTHORIZATION_HEADER);

        if (!StringUtils.hasText(auth) || !auth.startsWith(BASIC_PREFIX)) {
            sendUnauthorizedResponse(response);
            GalaxyLogger.warn(LogCategory.APP_LOG, "Missing or invalid Authorization header");
            return false;
        }

        try {
            String base64Credentials = auth.substring(BASIC_PREFIX.length()).trim();
            String credentials = new String(getDecoder().decode(base64Credentials), StandardCharsets.UTF_8);
            String[] values = credentials.split(":", 2);

            if (values.length != 2 ||
                    !isValidCredentials(values[0], values[1])) {
                sendUnauthorizedResponse(response);
                GalaxyLogger.warn(LogCategory.APP_LOG, "Invalid credentials attempt");
                return false;
            }

            return true;
        } catch (IllegalArgumentException e) {
            sendUnauthorizedResponse(response);
            GalaxyLogger.warn(LogCategory.APP_LOG, "Failed to decode basic authentication", e);
            return false;
        }
    }

    private boolean isValidCredentials(String username, String password) {
        return constantTimeEquals(username, swaggerProperties.getAuth().getUsername()) &&
                constantTimeEquals(password, swaggerProperties.getAuth().getPassword());
    }

    private void sendUnauthorizedResponse(HttpServletResponse response) {
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        response.setHeader("WWW-Authenticate", AUTHENTICATION_SCHEME);
    }

    private boolean constantTimeEquals(String a, String b) {
        byte[] bytes1 = a.getBytes(StandardCharsets.UTF_8);
        byte[] bytes2 = b.getBytes(StandardCharsets.UTF_8);
        return java.security.MessageDigest.isEqual(bytes1, bytes2);
    }
}
