package cn.com.chinastock.cnf.tongweb;

import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanFactoryPostProcessor;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.context.EnvironmentAware;
import org.springframework.core.PriorityOrdered;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.Environment;
import org.springframework.core.env.MapPropertySource;
import org.springframework.lang.NonNull;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

public class TongWebLicensePropertyConfigurator implements BeanFactoryPostProcessor, EnvironmentAware, PriorityOrdered {
    /**
     * 从 Intellij IDEA 的 Run 窗口中的 Environment Panel 中可以找到这个常量的定义
     */
    public static final String PROPERTY_SOURCES = "TongWebLicenseBootstrapPropertySources";
    private static final Map<String, Object> MAP_SOURCE;
    static {
        Map<String, Object> tempMap = new HashMap<>();
        tempMap.put("server.tongweb.license.path", "classpath:tongweb/license.dat");
        MAP_SOURCE = Collections.unmodifiableMap(tempMap);
    }

    private ConfigurableEnvironment environment;

    @Override
    public void postProcessBeanFactory(@NonNull ConfigurableListableBeanFactory beanFactory) throws BeansException {
        if (environment != null) {
            environment.getPropertySources().addFirst(new MapPropertySource(PROPERTY_SOURCES, MAP_SOURCE));
        }
    }

    @Override
    public void setEnvironment(@NonNull Environment environment) {
        if (environment instanceof ConfigurableEnvironment) {
            this.environment = (ConfigurableEnvironment) environment;
        }
    }

    @Override
    public int getOrder() {
        return Integer.MAX_VALUE;
    }
}

