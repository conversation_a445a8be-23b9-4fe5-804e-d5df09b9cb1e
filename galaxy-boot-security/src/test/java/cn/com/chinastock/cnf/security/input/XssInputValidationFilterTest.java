package cn.com.chinastock.cnf.security.input;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import org.junit.jupiter.api.Test;

import java.io.IOException;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.*;

class XssInputValidationFilterTest {
    @Test
    void should_skip_url_when_url_ends_with_gif() throws IOException, ServletException {
        // Given
        XssInputValidationFilter filter = new XssInputValidationFilter();
        HttpServletRequest request = mock(HttpServletRequest.class);
        ServletResponse response = mock(ServletResponse.class);
        FilterChain chain = mock(FilterChain.class);

        when(request.getRequestURI()).thenReturn("/static/image.gif");

        // When
        filter.doFilter(request, response, chain);

        // Then
        verify(chain).doFilter(request, response);
    }

    @Test
    void should_strip_xss_from_parameter_value() {
        // Given
        String input = "<script>alert('XSS')</script>";
        String expectedOutput = "&lt;script&gt;alert(&#39;XSS&#39;)&lt;/script&gt;";

        // When
        String result = XssSanitizerUtil.stripXSS(input);

        // Then
        assertThat(result).isEqualTo(expectedOutput);
    }

    @Test
    void should_return_null_when_parameter_value_is_null() {
        // Given
        String input = null;

        // When
        String result = XssSanitizerUtil.stripXSS(input);

        // Then
        assertThat(result).isNull();
    }

    @Test
    void should_return_empty_string_when_parameter_value_is_empty() {
        // Given
        String input = "";

        // When
        String result = XssSanitizerUtil.stripXSS(input);

        // Then
        assertThat(result).isEmpty();
    }
}
