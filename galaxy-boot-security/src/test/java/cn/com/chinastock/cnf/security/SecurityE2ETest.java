package cn.com.chinastock.cnf.security;

import cn.com.chinastock.cnf.security.controller.SecurityTestController;
import cn.com.chinastock.cnf.security.controller.TestModel;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@EnableAutoConfiguration
@SpringBootTest(
        webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,
        classes = {SecurityTestController.class},
        properties = {
                "galaxy.security.input-protect=true",
                "galaxy.security.output-protect=true"
        })
@AutoConfigureMockMvc
public class SecurityE2ETest {
    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    public void testXssOutputProtectionWithJsonContent() throws Exception {
        MvcResult result = mockMvc.perform(get("/api/security/unsafe-response")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andReturn();

        TestModel response = objectMapper.readValue(
                result.getResponse().getContentAsString(),
                TestModel.class
        );

        assertEquals(
                "&lt;script&gt;alert(&#39;xss&#39;)&lt;/script&gt;",
                response.getContent()
        );
    }

    @Test
    public void testXssInputProtectionWithJsonContent() throws Exception {
        TestModel model = new TestModel("<script>alert('xss')</script>", "<script>alert('xss')</script>");

        MvcResult result = mockMvc.perform(post("/api/security/echo-object")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(model)))
                .andExpect(status().isOk())
                .andReturn();

        TestModel response = objectMapper.readValue(
                result.getResponse().getContentAsString(),
                TestModel.class
        );

        // Input Protect 会转换为 &lt;script&gt;alert(&#39;xss&#39;)&lt;/script&gt;
        // Output Protect 会转换为 &amp;lt;script&amp;gt;alert(&amp;#39;xss&amp;#39;)&amp;lt;/script&amp;gt;
        assertEquals(
                "&amp;lt;script&amp;gt;alert(&amp;#39;xss&amp;#39;)&amp;lt;/script&amp;gt;",
                response.getContent()
        );
    }
}
