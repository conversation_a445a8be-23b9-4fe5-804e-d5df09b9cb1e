package cn.com.chinastock.cnf.security;

import javax.servlet.*;
import javax.servlet.http.HttpServletResponse;

import java.io.IOException;

public class OutputCheckFilter implements Filter {
    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {

        chain.doFilter(request, response);

        HttpServletResponse httpResponse = (HttpServletResponse) response;
        // 在此处添加输出检查逻辑
    }


    @Override
    public void destroy() {
    }
}