package cn.com.chinastock.cnf.security;

import cn.com.chinastock.cnf.core.log.GalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import cn.com.chinastock.cnf.security.csrf.CustomCsrfRequestMatcher;
import cn.com.chinastock.cnf.security.input.XssInputValidationFilter;
import cn.com.chinastock.cnf.security.output.CopiedFastJsonProperties;
import cn.com.chinastock.cnf.security.output.XssResponseBodyAdvice;
import org.springframework.boot.actuate.autoconfigure.security.servlet.EndpointRequest;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.provisioning.InMemoryUserDetailsManager;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.header.writers.ReferrerPolicyHeaderWriter;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@AutoConfiguration
@EnableConfigurationProperties({GalaxySecurityProperties.class, CopiedFastJsonProperties.class})
public class GalaxySecurityAutoConfiguration {
    private final GalaxySecurityProperties securityProperties;
    private final CopiedFastJsonProperties fastJsonProperties;

    public GalaxySecurityAutoConfiguration(GalaxySecurityProperties securityProperties, CopiedFastJsonProperties fastJsonProperties) {
        this.securityProperties = securityProperties;
        this.fastJsonProperties = fastJsonProperties;
    }

    @Bean
    @ConditionalOnProperty(prefix = "galaxy.security", name = "output-protect", havingValue = "true")
    public XssResponseBodyAdvice xssResponseBodyAdvice() {
        return new XssResponseBodyAdvice(fastJsonProperties);
    }

    @Bean
    @ConditionalOnProperty(prefix = "galaxy.security", name = "input-protect", havingValue = "true")
    public FilterRegistrationBean<XssInputValidationFilter> xssPreventFilter() {
        FilterRegistrationBean<XssInputValidationFilter> registrationBean = new FilterRegistrationBean<>();
        registrationBean.setFilter(new XssInputValidationFilter());
        registrationBean.addUrlPatterns("/*");
        return registrationBean;
    }

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        GalaxyLogger.info(LogCategory.FRAMEWORK_LOG, "securityProperties: " + securityProperties.toString());
        http.headers()
                .xssProtection()
                .block(true) // 等效于 ENABLED_MODE_BLOCK
                .and()
                .contentSecurityPolicy("default-src 'self'")
                .and()
                .referrerPolicy(ReferrerPolicyHeaderWriter.ReferrerPolicy.STRICT_ORIGIN)
                .and()
                .frameOptions()
                .deny();

        if (securityProperties.getActuator().isProtect()) {
            List<String> whitelist = securityProperties.getActuator().getWhitelist();
            if (whitelist != null && !whitelist.isEmpty()) {
                String ipExpression = whitelist.stream()
                        .map(ip -> "hasIpAddress('" + ip + "')")
                        .collect(Collectors.joining(" or "));

                http.requestMatcher(EndpointRequest.toAnyEndpoint())
                        .authorizeRequests()
                        .anyRequest()
                        .access(ipExpression);
            } else {
                http.requestMatcher(EndpointRequest.toAnyEndpoint())
                        .authorizeRequests()
                        .anyRequest().authenticated()
                        .and()
                        .httpBasic();
            }
        } else {
            http.authorizeRequests(authorize -> authorize.anyRequest().permitAll());
        }

        http.csrf(csrf -> {
            if (securityProperties.getCsrf().isProtect()) {
                List<String> whitelist = securityProperties.getCsrf().getWhitelist();
                List<String> blacklist = securityProperties.getCsrf().getBlacklist();
                csrf.requireCsrfProtectionMatcher(new CustomCsrfRequestMatcher(whitelist, blacklist));
            } else {
                csrf.disable();
            }
        });

        return http.build();
    }

    @Bean
    @ConditionalOnProperty(prefix = "galaxy.security", name = "cors.protect", havingValue = "true")
    public CorsFilter corsFilter() {
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        List<String> whitelist = securityProperties.getCors().getWhitelist();
        if (whitelist != null && !whitelist.isEmpty()) {
            CorsConfiguration configuration = new CorsConfiguration();
            configuration.setAllowCredentials(true);
            configuration.setAllowedOriginPatterns(whitelist);
            configuration.setAllowedMethods(Arrays.asList("GET", "POST", "PUT", "DELETE", "OPTIONS"));
            configuration.setAllowedHeaders(Arrays.asList("*"));
            source.registerCorsConfiguration("/**", configuration);
        } else {
            CorsConfiguration configuration = new CorsConfiguration();
            configuration.setAllowCredentials(false);
            configuration.setAllowedOrigins(Arrays.asList());
            configuration.setAllowedMethods(Arrays.asList());
            configuration.setAllowedHeaders(Arrays.asList());
            source.registerCorsConfiguration("/**", configuration);
        }

        return new CorsFilter(source);
    }

    @Bean
    @ConditionalOnClass(UserDetailsService.class)
    public UserDetailsService userDetailsService() {
        String name = securityProperties.getUser().getName();
        String password = securityProperties.getUser().getPassword();

        if (name == null || password == null) {
            return new InMemoryUserDetailsManager();
        }

        String encodePassword = passwordEncoder().encode(password);
        UserDetails user = User.withUsername(name)
                .password(encodePassword)
                .roles("USER")
                .build();

        return new InMemoryUserDetailsManager(user);
    }

    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }
}
