package cn.com.chinastock.cnf.security.output;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONWriter;
import org.springframework.web.util.HtmlUtils;

import java.util.ArrayList;

public class FastJsonFilterUtil {
    /**
     * 现在采用的是 Fastjson 2 版本的配置方式，即通过 JSONWriter.Feature 集合来配置序列化特性。
     * note: 需要同步修改 {@link cn.com.chinastock.cnf.fastjson.GalaxyFastJsonConfig#collectFromProperties()}
     *
     * @param fastJsonProperties FastJsonProperties
     * @return 序列化特性集合
     */
    private static ArrayList<JSONWriter.Feature> collectFromProperties(CopiedFastJsonProperties fastJsonProperties) {
        ArrayList<JSONWriter.Feature> features = new ArrayList<>();
        features.add(JSONWriter.Feature.WriteBigDecimalAsPlain);
        features.add(JSONWriter.Feature.IgnoreNonFieldGetter);

        if (fastJsonProperties.isWriteMapNullValue()) {
            features.add(JSONWriter.Feature.WriteMapNullValue);
        }

        if (fastJsonProperties.isWriteNullStringAsEmpty()) {
            features.add(JSONWriter.Feature.WriteNullStringAsEmpty);
        }

        return features;
    }

    public static Object handleFastJsonResponse(Object body, CopiedFastJsonProperties properties) {
        ArrayList<JSONWriter.Feature> fastjsonProperties = collectFromProperties(properties);

        try {
            if (body instanceof String) {
                Object jsonNode = JSON.parse(body.toString());
                Object sanitizedNode = sanitizeJsonNode(jsonNode);
                return toJsonString(sanitizedNode, fastjsonProperties);
            }

            String jsonString = toJsonString(body, fastjsonProperties);
            Object jsonNode = JSON.parse(jsonString);
            Object sanitizedNode = sanitizeJsonNode(jsonNode);
            return JSON.parseObject(toJsonString(sanitizedNode, fastjsonProperties), body.getClass());
        } catch (Exception e) {
            return body;
        }
    }

    private static String toJsonString(Object sanitizedNode, ArrayList<JSONWriter.Feature> fastjsonProperties) {
        return JSON.toJSONString(sanitizedNode, fastjsonProperties.toArray(new JSONWriter.Feature[0]));
    }

    private static Object sanitizeJsonNode(Object node) {
        if (node instanceof String) {
            return HtmlUtils.htmlEscape((String) node);
        } else if (node instanceof JSONObject) {
            JSONObject objectNode = (JSONObject) node;
            JSONObject newNode = new JSONObject();
            for (String key : objectNode.keySet()) {
                newNode.put(key, sanitizeJsonNode(objectNode.get(key)));
            }
            return newNode;
        } else if (node instanceof JSONArray) {
            JSONArray arrayNode = (JSONArray) node;
            JSONArray newNode = new JSONArray();
            for (Object element : arrayNode) {
                newNode.add(sanitizeJsonNode(element));
            }
            return newNode;
        }
        return node;
    }
}