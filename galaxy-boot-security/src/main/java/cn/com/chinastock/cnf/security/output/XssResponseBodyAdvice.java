package cn.com.chinastock.cnf.security.output;

import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;
import org.springframework.web.util.HtmlUtils;

@ControllerAdvice
public class XssResponseBodyAdvice implements ResponseBodyAdvice<Object> {
    private final CopiedFastJsonProperties fastJsonProperties;

    public XssResponseBodyAdvice(CopiedFastJsonProperties fastJsonProperties) {
        this.fastJsonProperties = fastJsonProperties;
    }

    @Override
    public boolean supports(MethodParameter returnType, Class<? extends HttpMessageConverter<?>> converterType) {
        return true;
    }

    @Override
    public Object beforeBodyWrite(Object body, MethodParameter returnType, MediaType selectedContentType,
                                  Class<? extends HttpMessageConverter<?>> selectedConverterType,
                                  ServerHttpRequest request, ServerHttpResponse response) {
        if (body == null) {
            return null;
        }

        // 如果选择的 Content-Type 是 JSON 类型
        if (selectedContentType.includes(MediaType.APPLICATION_JSON)) {
            String name = selectedConverterType.getName();
            // 如果使用的是 Spring 6，会使用 com.alibaba.fastjson2.support.spring6.http.converter.FastJsonHttpMessageConverter
            // 如果使用的是 Spring 5，会使用 will be com.alibaba.fastjson2.support.spring5.http.converter.FastJsonHttpMessageConverter
            if (name.contains("FastJsonHttpMessageConverter")) {
                return FastJsonFilterUtil.handleFastJsonResponse(body, fastJsonProperties);
            } else if (name.contains("MappingJackson2HttpMessageConverter")) {
                return FasterJsonFilterUtil.handleJacksonResponse(body);
            } else {
                // 其它场景，还可能包含 org.springframework.http.converter.StringHttpMessageConverter
                return FasterJsonFilterUtil.handleJacksonResponse(body);
            }
        }

        // 如果响应体是 String 类型，进行 HTML 转义处理
        if (body instanceof String) {
            return HtmlUtils.htmlEscape((String) body);
        } else {
            return body;
        }
    }
}