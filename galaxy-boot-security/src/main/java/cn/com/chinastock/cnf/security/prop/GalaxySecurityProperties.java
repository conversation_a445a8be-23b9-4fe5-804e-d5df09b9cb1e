package cn.com.chinastock.cnf.security.prop;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@ConfigurationProperties(prefix="galaxy.security")
public class GalaxySecurityProperties {
    private InputValidation inputValidation = new InputValidation();
    private OutputCheck outputCheck = new OutputCheck();
    private Csrf csrf = new Csrf();
    private Cors cors = new Cors();
    private Actuator actuator = new Actuator();

    public InputValidation getInputValidation() {
        return inputValidation;
    }

    public void setInputValidation(InputValidation inputValidation) {
        this.inputValidation = inputValidation;
    }

    public OutputCheck getOutputCheck() {
        return outputCheck;
    }

    public void setOutputCheck(OutputCheck outputCheck) {
        this.outputCheck = outputCheck;
    }

    public Csrf getCsrf() {
        return csrf;
    }

    public void setCsrf(Csrf csrf) {
        this.csrf = csrf;
    }

    public Cors getCors() {
        return cors;
    }

    public void setCors(Cors cors) {
        this.cors = cors;
    }

    public Actuator getActuator() {
        return actuator;
    }

    public void setActuator(Actuator actuator) {
        this.actuator = actuator;
    }

    public static class InputValidation {
        private boolean protect;

        public boolean isProtect() {
            return protect;
        }

        public void setProtect(boolean protect) {
            this.protect = protect;
        }
    }

    public static class OutputCheck {
        /**
         * 是否开启输出检查，开启后会对输出进行检查。
         * <p>默认为 false</p>
         * 检查事项如下：
         * <ul>
         *     <li>是否包含敏感信息</li>
         *     <li>是否包含 XSS 攻击代码</li>
         * </ul>
         */
        private boolean protect;

        public boolean isProtect() {
            return protect;
        }

        public void setProtect(boolean protect) {
            this.protect = protect;
        }
    }

    public static class Csrf {
        /**
         * 开启 CSRF 防护
         */
        private boolean protect;
        private List<String> whitelist;
        private List<String> blacklist;

        public boolean isProtect() {
            return protect;
        }

        public void setProtect(boolean protect) {
            this.protect = protect;
        }

        public List<String> getWhitelist() {
            return whitelist;
        }

        public void setWhitelist(List<String> whitelist) {
            this.whitelist = whitelist;
        }

        public List<String> getBlacklist() {
            return blacklist;
        }

        public void setBlacklist(List<String> blacklist) {
            this.blacklist = blacklist;
        }
    }

    public static class Cors {
        /**
         * 开启跨域保护
         */
        private boolean protect;
        /**
         * 跨域白名单
         */
        private List<String> whitelist;

        public boolean isProtect() {
            return protect;
        }

        public void setProtect(boolean protect) {
            this.protect = protect;
        }

        public List<String> getWhitelist() {
            return whitelist;
        }

        public void setWhitelist(List<String> whitelist) {
            this.whitelist = whitelist;
        }
    }

    public static class Actuator {
        /**
         * 开启 Actuator 保护
         */
        private boolean protect;

        public boolean isProtect() {
            return protect;
        }

        public void setProtect(boolean protect) {
            this.protect = protect;
        }

        @Override
        public String toString() {
            return "Actuator{" +
                    "protect=" + protect +
                    '}';
        }
    }

    @Override
    public String toString() {
        return "GalaxySecurityProperties{" +
                "inputValidation=" + inputValidation +
                ", outputCheck=" + outputCheck +
                ", csrf=" + csrf +
                ", cors=" + cors +
                ", actuator=" + actuator +
                '}';
    }
}